import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import EnhancedPdfViewer from './EnhancedPdfViewer';
import { modelOptions } from '../modelOptions';


function RoomTypeAnalysis() {
    const navigate = useNavigate();
    const location = useLocation();
    const [documentInfo, setDocumentInfo] = React.useState(null);
    const [roomTypeList, setRoomTypeList] = React.useState([]);
    const [pdfUrl, setPdfUrl] = React.useState(null);
    const [pdfFile, setPdfFile] = React.useState(null);
    const [selectedRoomType, setSelectedRoomType] = React.useState(null);
    const [roomTypeAnalysis, setRoomTypeAnalysis] = React.useState(null);

    const [editableCancellationPolicies, setEditableCancellationPolicies] = React.useState([{ CANCELLATION_FEE: '', START_DAY: '' }]); // New state for editable cancellation policies
    const [editablePayStayOffers, setEditablePayStayOffers] = React.useState([{ PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '', PERIODS: [] }]); // New state for editable pay stay offers
    const [editableMealType, setEditableMealType] = React.useState(''); // New state for editable meal type
    const [editableRoomCapacity, setEditableRoomCapacity] = React.useState({ BASE_CAPACITY: '', TOTAL_CAPACITY: '', MAX_ADULTS: '', MAX_CHILDREN: '' }); // New state for editable room capacity
    const [editableRoomRates, setEditableRoomRates] = React.useState([]); // New state for editable room rates
    // Store the initial groupedPeriods to reconstruct initialRates on cancel if needed
    const [initialGroupedPeriods, setInitialGroupedPeriods] = React.useState([]);
    const [searchText, setSearchText] = React.useState('');
    const [searchStatus, setSearchStatus] = React.useState(null);

    const [editingCancellationPolicy, setEditingCancellationPolicy] = React.useState(false);
    const [editingPayStay, setEditingPayStay] = React.useState(false);
    const [editingMealType, setEditingMealType] = React.useState(false);
    const [editingCapacity, setEditingCapacity] = React.useState(false);
    const [editingRates, setEditingRates] = React.useState(false);
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState(null);
    const [selectedModel, setSelectedModel] = React.useState('gemini-2.0-flash');
    const [selectedProperty, setSelectedProperty] = React.useState(null);
    const [periods, setPeriods] = React.useState([]);
    const [groupedPeriods, setGroupedPeriods] = React.useState([]);
    const [rateTypes, setRateTypes] = React.useState(['Normal']); // Default to 'Normal' if no rate types available
    const [workflowState, setWorkflowState] = React.useState(null); // Holds the loaded workflow object
    const [currentRoomTypeIndex, setCurrentRoomTypeIndex] = React.useState(0); // Index for step-by-step
    const [showCapacityDecreaseModal, setShowCapacityDecreaseModal] = React.useState(false);
    const [previousRoomCapacity, setPreviousRoomCapacity ] = React.useState({ BASE_CAPACITY: '', TOTAL_CAPACITY: '', MAX_ADULTS: '', MAX_CHILDREN: '' });
    const [isUserEditingCapacity, setIsUserEditingCapacity] = React.useState(false);
    const [pendingCapacity, setPendingCapacity] = React.useState(null);

    const storedBaseUrl = localStorage.getItem('baseUrl');
    const baseUrl = storedBaseUrl || 'http://localhost:6060';
    const csvFileID = localStorage.getItem('csvFileId'); // Retrieve file ID from localStorage
    console.log("csvFileID",csvFileID)

    // Save baseUrl to localStorage if not already stored
    if (!storedBaseUrl) {
        localStorage.setItem('baseUrl', baseUrl);
    }

    // Helper function to format dates as dd/mm/yyyy
    const formatDateToDDMMYYYY = (dateStr) => {
        if (!dateStr) return 'N/A';
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return 'Invalid Date';
        }
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    // Load data from localStorage on component mount
    React.useEffect(() => {
        const savedWorkflowState = localStorage.getItem('workflowState');
        const receivedFile = location.state?.file;
        const savedModel = localStorage.getItem('selectedModel');

        if (savedModel) setSelectedModel(savedModel);
        
        if (receivedFile) {
            setPdfFile(receivedFile);
            const fileUrl = URL.createObjectURL(receivedFile);
            setPdfUrl(fileUrl);
        } else {
            setError("PDF file not received. Please go back and try again.");
            setLoading(false);
            return;
        }

        if (savedWorkflowState) {
            const parsedState = JSON.parse(savedWorkflowState);
            setWorkflowState(parsedState);
            
            console.log("current property index", parsedState.currentPropertyIndex)
            console.log("total properties:", parsedState.properties.length)
            // Set the current property and room types from workflow state
            if (parsedState.currentPropertyIndex >= 0 && parsedState.currentPropertyIndex < parsedState.properties.length) {
                // Extract relevant info
    
              
        
                const currentProperty = parsedState.properties[parsedState.currentPropertyIndex];
                setSelectedProperty(currentProperty);
                const rooms = parsedState.currentPropertyRoomTypes || []; // Get room list for this property
                setRoomTypeList(rooms);
                const propertyPeriods = parsedState.currentPropertyPeriods
                console.log("new periods", parsedState.currentPropertyPeriods)

                // Set rate types from workflow state
                if (parsedState.rateTypes && parsedState.rateTypes.distinct_weekly_rates) {
                    setRateTypes(parsedState.rateTypes.distinct_weekly_rates);
                    console.log("Rate types from workflow state:", parsedState.rateTypes.distinct_weekly_rates);
                } else {
                    console.log("No rate types found in workflow state, using default ['Normal']");
                    setRateTypes(['Normal']);
                }

                // Set periods from workflow state
                if (propertyPeriods) {

                    setPeriods(propertyPeriods);
                    
                    // Group periods by name
                    const grouped = propertyPeriods.reduce((acc, period) => {
                        const name = period[0];
                        console.log("acc:", acc)
                        console.log("name", name)
                        if (!acc[name]) {
                            acc[name] = {
                                name,
                                dateRanges: []
                            };
                        }
                        acc[name].dateRanges.push({
                            start: period[1],
                            end: period[2]
                        });
                        return acc;
                    }, {});
                    
                    setGroupedPeriods(Object.values(grouped));
                    setInitialGroupedPeriods(Object.values(grouped)); // Store initial grouped periods
                    // Check if room list is valid and trigger analysis for the first room type
                    if (rooms.length > 0 && currentRoomTypeIndex < rooms.length) {
                        const currentRoom = rooms[currentRoomTypeIndex];
                        setSelectedRoomType(currentRoom);
                        console.log(`Workflow: Analyzing Room Type ${currentRoomTypeIndex + 1} / ${rooms.length}: ${currentRoom} for Property: ${currentProperty}`);
                        fetchRoomTypeAnalysis(currentRoom, currentProperty, parsedState.documentFilename, propertyPeriods, Object.values(grouped)); // Pass necessary info
                    } else if (rooms.length === 0) {
                        console.log(`Workflow: No room types found for property ${currentProperty}. Proceeding to next step.`);
                        // If no rooms, immediately trigger the logic to move to the next property/finish
                        handleProceed(); // Call handleProceed to move on
                    } else {
                        setError(`Invalid initial room type index (${currentRoomTypeIndex}) for property ${currentProperty}.`);
                    }

                }
            }
            else
            {
                console.log("Invalid property index")
            }
        } else {
            setError("Workflow state not found in localStorage. Please start from the property analysis page.");
            setLoading(false);
        }

        // Cleanup function remains similar
        return () => {
            // No need to revoke if using Data URL
        };
    }, []);

        const handleBackNavigation = () => {
        const savedWorkflowStateString = localStorage.getItem('workflowState');
        const currentPdfFile = pdfFile || location.state?.file; // Use state first, then location state

        if (!currentPdfFile) {
            console.error("pdfFile is not available. Navigating to /property-analysis as a fallback.");
            navigate('/property-analysis'); // True fallback
            return;
        }

        if (!savedWorkflowStateString) {
            console.warn("Workflow state not found in localStorage. Navigating to /property-analysis.");
            navigate('/property-analysis', { state: { file: currentPdfFile } });
            return;
        }

        try {
            const parsedWorkflowState = JSON.parse(savedWorkflowStateString);

            if (!parsedWorkflowState ||
                typeof currentRoomTypeIndex !== 'number' ||
                !Array.isArray(parsedWorkflowState.currentPropertyRoomTypes)
            ) {
                console.warn("Essential data missing or malformed in workflowState from localStorage. Navigating to /property-analysis.");
                console.log(parsedWorkflowState)
                navigate('/property-analysis', { state: { file: currentPdfFile } });
                return;
            }

            const currentIdx = currentRoomTypeIndex;
            const currentRoomTypes = parsedWorkflowState.currentPropertyRoomTypes;

            if (currentIdx > 0) {
                const newRoomTypeIndex = currentIdx - 1;

                if (newRoomTypeIndex < 0 || newRoomTypeIndex >= currentRoomTypes.length) {
                    console.error("New room type index is out of bounds. Navigating to /property-analysis.");
                    navigate('/property-analysis', { state: { file: currentPdfFile } });
                    return;
                }
                const newSelectedRoomType = currentRoomTypes[newRoomTypeIndex];

                const updatedWorkflowStateForStorage = {
                    ...parsedWorkflowState,
                    currentRoomTypeIndex: newRoomTypeIndex,
                };
                localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowStateForStorage));

                setCurrentRoomTypeIndex(newRoomTypeIndex);
                setSelectedRoomType(newSelectedRoomType);
                setRoomTypeAnalysis(null);
                setError(null);

                // Ensure component's state variables for fetch are valid
                if (!selectedProperty || !workflowState || !workflowState.documentFilename || !periods || !groupedPeriods) {
                     console.error("Required data for fetchRoomTypeAnalysis (selectedProperty, documentFilename, periods, groupedPeriods) is missing from component state. Navigating to /property-analysis.");
                     navigate('/property-analysis', { state: { file: currentPdfFile } });
                     return;
                }

                fetchRoomTypeAnalysis(
                    newSelectedRoomType,
                    selectedProperty,
                    workflowState.documentFilename,
                    periods,
                    groupedPeriods
                );

            } else {
                // First room type (index 0), navigate to PropertyAnalysis
                navigate('/property-analysis', { state: { file: currentPdfFile } });
            }
        } catch (e) {
            console.error("Error parsing workflowState or during back navigation logic:", e);
            navigate('/property-analysis', { state: { file: currentPdfFile } });
        }
    };

    const fetchRoomTypeAnalysis = async (roomType, propertyName, filename, periodsData, groupedPeriodsData, capacityOverride = null) => {
        setLoading(true);
        setError(null);
       
        // Initialize empty analysis state
        setRoomTypeAnalysis({
            totalCapacity: null,
            cancellationPolicy: null,
            payStay: null,
            mealTypeData: null,
            roomCapacityData: null,
            rates: []
        });

        try {
            
            const currentModel = localStorage.getItem('selectedModel') || selectedModel;

            // Log the request details
            console.log('Request details:', {
                filename: filename,
                property: propertyName,
                room_type: roomType,
                model: currentModel
            });


            const meal_types_available = localStorage.getItem("mealBasis") || "Unknown";
            // Fetch cancellation policy and pay-stay information in parallel
            const [cancellationPolicyResponse, payStayResponse, mealTypeResponse, roomCapacityResponse] = await Promise.all([
                fetch(`${baseUrl}/room-cancellation-policy`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel
                    }),
                }),
                fetch(`${baseUrl}/room-pay-stays`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel,
                        periods: periodsData,
                    }),
                }),
                fetch(`${baseUrl}/room-meal-type`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel,
                        meal_types: meal_types_available
                    }),
                }),

                fetch(`${baseUrl}/room-capacity`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        filename: filename,
                        property_name: propertyName,
                        room_type: roomType,
                        model: currentModel
                    }),
                })

            ]);

            if (!cancellationPolicyResponse.ok) {
                const cancellationPolicyError = await cancellationPolicyResponse.text();
                console.error('Error details:', {
                    cancellationPolicy: cancellationPolicyError
                });
                throw new Error(`Failed to fetch room type analysis. Cancellation Policy: ${cancellationPolicyResponse.status}`);
            }

            if (!payStayResponse.ok) {
                const payStayError = await payStayResponse.text();
                console.error('Error details:', {
                    payStay: payStayError
                });
                throw new Error(`Failed to fetch room type analysis. Pay Stay: ${payStayResponse.status}`);
            }
            if (!roomCapacityResponse.ok) {
                const roomCapacityResponseError = await roomCapacityResponse.text();
                console.error('Error details:', {
                    roomCapacity: roomCapacityResponseError
                });
                throw new Error(`Failed to fetch room type analysis. Capacity: ${roomCapacityResponse.status}`);
            }
            if (!mealTypeResponse.ok) {
                const mealTypeResponseError = await mealTypeResponse.text();
                console.error('Error details:', {
                    mealType: mealTypeResponseError
                });
                throw new Error(`Failed to fetch room type analysis. MealType: ${mealTypeResponse.status}`);
            }


            const cancellationPolicyData = await cancellationPolicyResponse.json();
            const payStayData = await payStayResponse.json();
            const roomCapacityData = await roomCapacityResponse.json();
            const mealTypeData = await mealTypeResponse.json();

            console.log("cancellationPolicyData",cancellationPolicyData);
            console.log("payStayData",payStayData);
            console.log("roomCapacityData",roomCapacityData)
            console.log("mealTypeData",mealTypeData)


            // Update state with analysis data
            setRoomTypeAnalysis(prev => ({
                ...prev,
                cancellationPolicyData: cancellationPolicyData,
                payStayData: payStayData,
                mealTypeData: mealTypeData,
                roomCapacityData: roomCapacityData
            }));

            // Initialize editableCancellationPolicies
            if (cancellationPolicyData && cancellationPolicyData.cancellation_policy && cancellationPolicyData.cancellation_policy.POLICIES && cancellationPolicyData.cancellation_policy.POLICIES.length > 0) {
                setEditableCancellationPolicies(cancellationPolicyData.cancellation_policy.POLICIES);
            } else {
                setEditableCancellationPolicies([{ CANCELLATION_FEE: '', START_DAY: '' }]); // Default for empty inputs
            }

            // Initialize editablePayStayOffers
            if (payStayData && payStayData.pay_stays && payStayData.pay_stays.OFFERS && payStayData.pay_stays.OFFERS.length > 0) {
                setEditablePayStayOffers(payStayData.pay_stays.OFFERS);
            } else {
                setEditablePayStayOffers([{ PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '', PERIODS: [] }]); // Default for empty inputs
            }

            // Initialize editableMealType
            if (mealTypeData && mealTypeData.meal_type !== undefined) {
                setEditableMealType(mealTypeData.meal_type);
            } else {
                setEditableMealType(''); // Default to empty string
            }

            // Initialize editableRoomCapacity
            if (roomCapacityData && roomCapacityData.capacity) {
                if (!isUserEditingCapacity) {
                    setEditableRoomCapacity({
                        BASE_CAPACITY: roomCapacityData.capacity.BASE_CAPACITY !== undefined ? roomCapacityData.capacity.BASE_CAPACITY : '',
                        TOTAL_CAPACITY: roomCapacityData.capacity.TOTAL_CAPACITY !== undefined ? roomCapacityData.capacity.TOTAL_CAPACITY : '',
                        MAX_ADULTS: roomCapacityData.capacity.MAX_ADULTS !== undefined ? roomCapacityData.capacity.MAX_ADULTS : '',
                        MAX_CHILDREN: roomCapacityData.capacity.MAX_CHILDREN !== undefined ? roomCapacityData.capacity.MAX_CHILDREN : ''
                    });
                }
            } else {
                if (!isUserEditingCapacity) {
                    setEditableRoomCapacity({ BASE_CAPACITY: '', TOTAL_CAPACITY: '', MAX_ADULTS: '', MAX_CHILDREN: '' });
                }
            }

            // Use the override if provided, otherwise use API data
            const effectiveCapacity = capacityOverride || (roomCapacityData && roomCapacityData.capacity) || { BASE_CAPACITY: 0, TOTAL_CAPACITY: 0, MAX_ADULTS: 0, MAX_CHILDREN: 0 };
            const totalCapacity = effectiveCapacity.TOTAL_CAPACITY || 0;
            const maxAdults = effectiveCapacity.MAX_ADULTS || 0;
            const maxChildren = effectiveCapacity.MAX_CHILDREN || 0;

            // Update state with total capacity
            setRoomTypeAnalysis(prev => ({
                ...prev,
                totalCapacity
            }));

            // Initialize rates array with empty objects for each period
            console.log("groupedPeriodsData", groupedPeriodsData)
            console.log("rateTypes", rateTypes)

            // Create initial rate structure that accommodates multiple rate types
            const createEmptyRateStructure = () => {
                const structure = {};
                ['single', 'double', 'triple', 'quad', 'child', 'infant'].forEach(roomRateType => {
                    structure[roomRateType] = {};
                    rateTypes.forEach(rateType => {
                        structure[roomRateType][rateType] = null;
                    });
                });
                return structure;
            };

            const initialRates = groupedPeriodsData.map(period => ({
                period: period.name,
                dateRanges: period.dateRanges,
                rates: period.dateRanges.map(() => createEmptyRateStructure())
            }));

            setRoomTypeAnalysis(prev => ({
                ...prev,
                rates: initialRates
            }));
            setEditableRoomRates(initialRates); // Initialize editableRoomRates

            // Fetch rates for each period and date range
            for (let i = 0; i < groupedPeriodsData.length; i++) {
                const period = groupedPeriodsData[i];
                console.log("Processing period:", period);
                for (let j = 0; j < period.dateRanges.length; j++) {
                    const dateRange = period.dateRanges[j];
                    const periodData = [period.name, dateRange.start, dateRange.end];
                    
                    try {
                        // Fetch rates for each rate type
                        for (let k = 0; k < rateTypes.length; k++) {
                            const rateType = rateTypes[k];
                            console.log(`Fetching single room rate for period: ${periodData}, rate type: ${rateType}`);

                            // Fetch single room rate for this rate type
                            const singleResponse = await fetch(`${baseUrl}/room-single-room-rate`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    filename: filename,
                                    property_name: propertyName,
                                    room_type: roomType,
                                    period: periodData,
                                    rate_type: rateType,
                                    model: currentModel
                                }),
                            });
                            if (singleResponse.ok) {
                                const singleData = await singleResponse.json();
                                console.log(`Single room rate data for ${rateType}:`, singleData);

                                // Check for malformed data before conversion
                                if (singleData && singleData.single_room_rate && singleData.single_room_rate.room_rate_package) {
                                    const pkg = singleData.single_room_rate.room_rate_package;
                                    Object.keys(pkg).forEach(key => {
                                        if (pkg[key] && typeof pkg[key] === 'object' && pkg[key].hasOwnProperty('desc') && pkg[key].hasOwnProperty('type')) {
                                            console.warn(`Malformed data detected in ${rateType} rate for key '${key}':`, pkg[key]);
                                        }
                                    });
                                }

                                // Convert new format to old format for compatibility
                                const convertedData = convertNewFormatToOld(singleData, 'single');

                                // Update the rates array with the converted data
                                setRoomTypeAnalysis(prev => {
                                    const newRates = [...prev.rates];
                                    if (newRates[i] && newRates[i].rates[j] && newRates[i].rates[j].single) {
                                        newRates[i].rates[j].single[rateType] = convertedData || singleData;
                                    } else {
                                        console.warn(`Index out of bounds: i=${i}, j=${j}, newRates.length=${newRates.length}, newRates[i]=${newRates[i]}`);
                                    }
                                    return { ...prev, rates: newRates };
                                });
                                // Update editableRoomRates as well
                                setEditableRoomRates(prevEditableRates => {
                                    const newEditableRates = JSON.parse(JSON.stringify(prevEditableRates)); // Deep copy
                                    if (newEditableRates[i] && newEditableRates[i].rates[j] && newEditableRates[i].rates[j].single) {
                                        newEditableRates[i].rates[j].single[rateType] = convertedData || singleData;
                                    }
                                    return newEditableRates;
                                });
                            } else {
                                console.error(`Error getting single Room Rate for ${rateType}:`, await singleResponse.text());
                            }

                            // Fetch double room rate if capacity >= 2
                            if (maxAdults >= 2) {
                                const doubleResponse = await fetch(`${baseUrl}/room-double-room-rate`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        filename: filename,
                                        property_name: propertyName,
                                        room_type: roomType,
                                        period: periodData,
                                        rate_type: rateType,
                                        model: currentModel
                                    }),
                                });
                                if (doubleResponse.ok) {
                                    const doubleData = await doubleResponse.json();
                                    console.log(`Double room rate data for ${rateType}:`, doubleData);

                                    // Convert new format to old format for compatibility
                                    const convertedData = convertNewFormatToOld(doubleData, 'double');

                                    setRoomTypeAnalysis(prev => {
                                        const newRates = [...prev.rates];
                                        if (newRates[i] && newRates[i].rates[j] && newRates[i].rates[j].double) {
                                            newRates[i].rates[j].double[rateType] = convertedData || doubleData;
                                        } else {
                                            console.warn(`Index out of bounds: i=${i}, j=${j}, newRates.length=${newRates.length}, newRates[i]=${newRates[i]}`);
                                        }
                                        return { ...prev, rates: newRates };
                                    });
                                    // Update editableRoomRates as well
                                    setEditableRoomRates(prevEditableRates => {
                                        const newEditableRates = JSON.parse(JSON.stringify(prevEditableRates)); // Deep copy
                                        if (newEditableRates[i] && newEditableRates[i].rates[j] && newEditableRates[i].rates[j].double) {
                                            newEditableRates[i].rates[j].double[rateType] = convertedData || doubleData;
                                        }
                                        return newEditableRates;
                                    });
                                } else {
                                    console.error(`Error getting double Room Rate for ${rateType}:`, await doubleResponse.text());
                                }
                            }

                            // Fetch child rates for all categories
                            const childAgeRanges = JSON.parse(localStorage.getItem('childAgeRanges') || "[]");
                            const childCategories = childAgeRanges.map(range => `${range[0]}-${range[1]}`);

                            const childResponse = await fetch(`${baseUrl}/room-children-rates`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    filename: filename,
                                    property_name: propertyName,
                                    room_type: roomType,
                                    period: periodData,
                                    rate_type: rateType,
                                    model: currentModel,
                                    child_categories: childAgeRanges
                                }),
                            });
                            if (childResponse.ok) {
                                const childData = await childResponse.json();
                                console.log("Child rate data:", childData)
                                // Convert new children rates format to old format for compatibility
                                const convertedData = convertChildrenRatesFormatToOld(childData);

                                setRoomTypeAnalysis(prev => {
                                    const newRates = [...prev.rates];
                                    if (newRates[i] && newRates[i].rates[j] && newRates[i].rates[j].child) {
                                        newRates[i].rates[j].child[rateType] = convertedData || childData;
                                    } else {
                                        console.warn(`Index out of bounds for child rate: i=${i}, j=${j}, newRates.length=${newRates.length}, newRates[i]=${newRates[i]}`);
                                    }
                                    return { ...prev, rates: newRates };
                                });
                                // Update editableRoomRates as well
                                setEditableRoomRates(prevEditableRates => {
                                    const newEditableRates = JSON.parse(JSON.stringify(prevEditableRates)); // Deep copy
                                    if (newEditableRates[i] && newEditableRates[i].rates[j] && newEditableRates[i].rates[j].child) {
                                        newEditableRates[i].rates[j].child[rateType] = convertedData || childData;
                                    }
                                    return newEditableRates;
                                });
                            } else {
                                console.error(`Error getting child rates for ${rateType}:`, await childResponse.text());
                            }

                            // Fetch infant rate
                            const infantResponse = await fetch(`${baseUrl}/room-infant-rate`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({
                                    filename: filename,
                                    property_name: propertyName,
                                    room_type: roomType,
                                    period: periodData,
                                    rate_type: rateType,
                                    model: currentModel
                                }),
                            });
                            if (infantResponse.ok) {
                                const infantData = await infantResponse.json();
                                console.log(`Infant rate data for ${rateType}:`, infantData);

                                // Convert new format to old format for compatibility
                                const convertedData = convertChildInfantFormatToOld(infantData, 'infant');

                                setRoomTypeAnalysis(prev => {
                                    const newRates = [...prev.rates];
                                    if (newRates[i] && newRates[i].rates[j] && newRates[i].rates[j].infant) {
                                        newRates[i].rates[j].infant[rateType] = convertedData || infantData;
                                    } else {
                                        console.warn(`Index out of bounds for infant rate: i=${i}, j=${j}, newRates.length=${newRates.length}, newRates[i]=${newRates[i]}`);
                                    }
                                    return { ...prev, rates: newRates };
                                });
                                // Update editableRoomRates as well
                                setEditableRoomRates(prevEditableRates => {
                                    const newEditableRates = JSON.parse(JSON.stringify(prevEditableRates)); // Deep copy
                                    if (newEditableRates[i] && newEditableRates[i].rates[j] && newEditableRates[i].rates[j].infant) {
                                        newEditableRates[i].rates[j].infant[rateType] = convertedData || infantData;
                                    }
                                    return newEditableRates;
                                });
                            } else {
                                console.error(`Error getting infant rate for ${rateType}:`, await infantResponse.text());
                            }

                            // Fetch triple room rate if capacity >= 3
                            if (maxAdults >= 3) {
                                const tripleResponse = await fetch(`${baseUrl}/room-triple-room-rate`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        filename: filename,
                                        property_name: propertyName,
                                        room_type: roomType,
                                        period: periodData,
                                        rate_type: rateType,
                                        model: currentModel
                                    }),
                                });
                                if (tripleResponse.ok) {
                                    const tripleData = await tripleResponse.json();
                                    console.log(`Triple room rate data for ${rateType}:`, tripleData);

                                    // Convert new format to old format for compatibility
                                    const convertedData = convertNewFormatToOld(tripleData, 'triple');

                                    setRoomTypeAnalysis(prev => {
                                        const newRates = [...prev.rates];
                                        if (newRates[i] && newRates[i].rates[j] && newRates[i].rates[j].triple) {
                                            newRates[i].rates[j].triple[rateType] = convertedData || tripleData;
                                        } else {
                                            console.warn(`Index out of bounds for triple rate: i=${i}, j=${j}, newRates.length=${newRates.length}, newRates[i]=${newRates[i]}`);
                                        }
                                        return { ...prev, rates: newRates };
                                    });
                                    // Update editableRoomRates as well
                                    setEditableRoomRates(prevEditableRates => {
                                        const newEditableRates = JSON.parse(JSON.stringify(prevEditableRates)); // Deep copy
                                        if (newEditableRates[i] && newEditableRates[i].rates[j] && newEditableRates[i].rates[j].triple) {
                                            newEditableRates[i].rates[j].triple[rateType] = convertedData || tripleData;
                                        }
                                        return newEditableRates;
                                    });
                                } else {
                                    console.error(`Error getting triple room rate for ${rateType}:`, await tripleResponse.text());
                                }
                            }

                            // Fetch quad room rate if capacity >= 4
                            if (maxAdults >= 4) {
                                const quadResponse = await fetch(`${baseUrl}/room-quad-room-rate`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        filename: filename,
                                        property_name: propertyName,
                                        room_type: roomType,
                                        period: periodData,
                                        rate_type: rateType,
                                        model: currentModel
                                    }),
                                });
                                if (quadResponse.ok) {
                                    const quadData = await quadResponse.json();
                                    console.log(`Quad room rate data for ${rateType}:`, quadData);

                                    // Convert new format to old format for compatibility
                                    const convertedData = convertNewFormatToOld(quadData, 'quad');

                                    setRoomTypeAnalysis(prev => {
                                        const newRates = [...prev.rates];
                                        if (newRates[i] && newRates[i].rates[j] && newRates[i].rates[j].quad) {
                                            newRates[i].rates[j].quad[rateType] = convertedData || quadData;
                                        } else {
                                            console.warn(`Index out of bounds for quad rate: i=${i}, j=${j}, newRates.length=${newRates.length}, newRates[i]=${newRates[i]}`);
                                        }
                                        return { ...prev, rates: newRates };
                                    });
                                    // Update editableRoomRates as well
                                    setEditableRoomRates(prevEditableRates => {
                                        const newEditableRates = JSON.parse(JSON.stringify(prevEditableRates)); // Deep copy
                                        if (newEditableRates[i] && newEditableRates[i].rates[j] && newEditableRates[i].rates[j].quad) {
                                            newEditableRates[i].rates[j].quad[rateType] = convertedData || quadData;
                                        }
                                        return newEditableRates;
                                    });
                                } else {
                                    console.error(`Error getting quad room rate for ${rateType}:`, await quadResponse.text());
                                }
                            }
                        } // End of rate type loop
                        
                        
                    } catch (err) {
                        console.error(`Error fetching rates for period ${period.name} date range ${j}:`, err);
                        setRoomTypeAnalysis(prev => ({
                            ...prev,
                            rates: prev.rates.map((r, periodIndex) => 
                                periodIndex === i ? {
                                    ...r,
                                    rates: r.rates.map((rate, rangeIndex) => 
                                        rangeIndex === j ? { ...rate, error: err.message } : rate
                                    )
                                } : r
                            )
                        }));
                    }
                }
            }
        } catch (err) {
            console.error('Full error:', err);
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const handleProceed = async () => {
        if (!workflowState || !roomTypeList) {
             setError("Cannot proceed, workflow state or room list missing.");
             return;
        }

        const nextRoomTypeIndex = currentRoomTypeIndex + 1;

        //create a dictionary to represent the row in the csv: 
        // "Property", "Room type", "Meal basis", "Includes", "Excludes", "Child 1 From age", "Child 1 To age", "Max adults", "Max Children", "Max A+C", "Cancellation Policy from days 1", "Cancellation fee % 1", "Pay stay days", "Pay stay free nights"
        const csvRows = generateRows();
        
        if (!csvFileID) {
            console.error("File ID not found in localStorage. Cannot append rows.");
            setError("File ID not found. Please ensure the file is initialized.");
        } else {
            try {
                // Append each row sequentially
                for (const csvRow of csvRows) {
                    const response = await fetch(`${baseUrl}/append-row`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            file_id: csvFileID,
                            data: csvRow
                        }),
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Failed to append row: ${errorText}`);
                    }
                }

                console.log("All rows successfully appended to the file.");
            } catch (err) {
                console.error("Error appending rows:", err);
                setError(`Failed to append rows: ${err.message}`);
            }
        }

        // Check if there are more room types for the CURRENT property
        if (nextRoomTypeIndex < roomTypeList.length) {
            // --- Move to the next room type ---
            console.log(`Workflow: Moving to next room type index: ${nextRoomTypeIndex}`);
            setCurrentRoomTypeIndex(nextRoomTypeIndex); // Update index state
            const nextRoomType = roomTypeList[nextRoomTypeIndex];
            setSelectedRoomType(nextRoomType); // Update selected room state
            setRoomTypeAnalysis(null); // Clear previous analysis
            setError(null); // Clear previous errors

            // Fetch analysis for the new room type
            fetchRoomTypeAnalysis(
                nextRoomType,
                selectedProperty, // Already in state
                workflowState.documentFilename, // From workflow state
                periods, // Already in state
                groupedPeriods // Already in state
            );

        } else {
            // --- Last room type for this property is done, move to next property or finish ---
            console.log(`Workflow: Finished all room types for property: ${selectedProperty}`);
            const nextPropertyIndex = workflowState.currentPropertyIndex + 1;

            // Create a mutable copy to update
            let updatedWorkflowState = { ...workflowState };
            updatedWorkflowState.currentPropertyIndex = nextPropertyIndex;

            // Check if there are more properties in the overall workflow
            if (nextPropertyIndex < workflowState.totalProperties) {
                // --- Go to the next property ---
                console.log(`Workflow: Proceeding to next property index: ${nextPropertyIndex}`);
                 try {
                    // Clear room-specific details before saving for next property page
                    delete updatedWorkflowState.currentPropertyRoomTypes;
                    localStorage.setItem('cameFromRoomTypeAnalysisLastStep', 'true'); // Added flag
                    localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowState));
                    // Navigate back to property analysis page
                    navigate('/property-analysis', { state: { file: pdfFile } });
                 } catch (error) {
                     setError("Failed to save workflow state before navigating to next property.");
                     console.error("localStorage error:", error);
                 }
            } else {
                // --- All properties and room types are done ---
                console.log("Workflow: All properties processed. Checking for next section.");
                localStorage.setItem('cameFromRoomTypeAnalysisLastStep', 'true'); // Also set here

                // Check if there are more sections to process
                const nextSectionIndex = workflowState.currentSectionIndex + 1;
                if (nextSectionIndex < workflowState.sectionNames.length) {
                    // Update workflow state for next section
                     const updatedWorkflowStateForNextSection = { // Renamed variable to avoid conflict
                        ...workflowState,
                        currentSectionIndex: nextSectionIndex,
                        currentPropertyIndex: 0, // Reset property index for new section
                       documentFilename: workflowState.sectionNames[nextSectionIndex].name, // Update filename for new section
                        // Clear property-specific data for the new section
                        currentPropertyRoomTypes: undefined,
                        currentPropertyPeriods: undefined,
                        currentPropertyGroupedPeriods: undefined,
                    };
                    delete updatedWorkflowStateForNextSection.currentPropertyRoomTypes;
                    delete updatedWorkflowStateForNextSection.currentPropertyPeriods;
                    delete updatedWorkflowStateForNextSection.currentPropertyGroupedPeriods;

                    // Store updated workflow state
                    localStorage.setItem('workflowState', JSON.stringify(updatedWorkflowStateForNextSection));

                    // Navigate back to the next section
                    navigate('/', { state: { file: pdfFile } });
                } else {
                    // No more sections, proceed with CSV download
                    fetch(`${baseUrl}/download-csv/${csvFileID}`, {
                        method: 'GET',
                    })
                    .then(response => {
                        if (!response.ok) {
                            return response.text().then(text => {
                                throw new Error(`Failed to download CSV (HTTP ${response.status}): ${text}`);
                            });
                        }
                        
                        let filename = workflowState.documentFilename || `extracted_data_${csvFileID}.csv`;
                        if (filename.toLowerCase().endsWith('.txt')){
                            filename = filename.slice(0, -4);
                        }
                        if (!filename.toLowerCase().endsWith('.csv')) {
                            filename += '.csv';
                        }
                        return response.blob().then(blob => ({ blob, filename }));
                    })
                    .then(({ blob, filename }) => {
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                        
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        
                        console.log("CSV download initiated successfully.");
                        
                        // After successful download, check for more files
                        const filesFromStorage = localStorage.getItem('pendingFiles');
                        if (filesFromStorage) {
                            try {
                                const pendingFiles = JSON.parse(filesFromStorage);
                                if (pendingFiles.length > 0) {
                                    // Remove the first file (current one) and update storage
                                    pendingFiles.shift();
                                    localStorage.setItem('pendingFiles', JSON.stringify(pendingFiles));
                                    
                                    if (pendingFiles.length > 0) {
                                        console.log(`Workflow: Moving to next file. ${pendingFiles.length} files remaining.`);
                                        localStorage.setItem('processNextFile', 'true');
                                        localStorage.removeItem('workflowState');
                                        localStorage.removeItem('csvFileID');
                                        // Pass information about remaining files through navigation state
                                        navigate('/', {
                                            state: {
                                                needsFileReselection: true,
                                                remainingFiles: pendingFiles,
                                                message: `Please reselect your PDF files to continue processing the remaining ${pendingFiles.length} file(s): ${pendingFiles.join(', ')}`
                                            }
                                        });
                                        return;
                                    }
                                }
                            } catch (err) {
                                console.error('Error parsing pending files:', err);
                            }
                        }
                        
                        // If no more files or error occurred, clean up and navigate home
                        try {
                            localStorage.removeItem('workflowState');
                            localStorage.removeItem('csvFileID');
                            navigate('/');
                        } catch (error) {
                            setError("Failed to clear workflow state after completion.");
                            console.error("localStorage error:", error);
                        }
                    })
                    .catch(err => {
                        console.error("Error during CSV download process:", err);
                        setError(`Failed to download CSV. ${err.message}. Please check the console for details.`);
                    });
                }
            }
      
        }
    };

    const handleCancellationPolicyChange = (index, field, value) => {
        const updatedPolicies = [...editableCancellationPolicies];
        updatedPolicies[index] = { ...updatedPolicies[index], [field]: value };
        setEditableCancellationPolicies(updatedPolicies);
    };

    const handlePayStayChange = (index, field, value) => {
        const updatedOffers = [...editablePayStayOffers];
        updatedOffers[index] = { ...updatedOffers[index], [field]: value };
        setEditablePayStayOffers(updatedOffers);
    };

    const handleRoomCapacityChange = (field, value) => {
        setEditableRoomCapacity(prev => ({ ...prev, [field]: value }));
    };

    const handleRateChange = (periodIndex, rangeIndex, rateType, field, value, rateTypeName = null) => {
        setEditableRoomRates(prevRates => {
            const newRates = JSON.parse(JSON.stringify(prevRates)); // Deep copy

            // Handle new nested structure with rate types
            if (
                rateTypeName &&
                newRates[periodIndex] &&
                newRates[periodIndex].rates[rangeIndex] &&
                newRates[periodIndex].rates[rangeIndex][rateType] &&
                newRates[periodIndex].rates[rangeIndex][rateType][rateTypeName]
            ) {
                // For room rates (single, double, triple, quad)
                const rateObj = newRates[periodIndex].rates[rangeIndex][rateType][rateTypeName][`${rateType}_room_rate`];
                if (rateObj) {
                    if (["BASE_RATE", "ADDITIONAL_PEOPLE", "MIN_STAY_NIGHTS", "ADDITIONAL_PERSON_COST"].includes(field)) {
                        rateObj[field] = parseFloat(value) || 0;
                    } else if (["RATE_UNIT", "CURRENCY"].includes(field)) {
                        rateObj[field] = value;
                    }
                }
                // For child/infant rates
                else if (newRates[periodIndex].rates[rangeIndex][rateType][rateTypeName][`${rateType}_rate`]) {
                    newRates[periodIndex].rates[rangeIndex][rateType][rateTypeName][`${rateType}_rate`][field] = parseFloat(value) || 0;
                }
            }
            // Handle old structure for backward compatibility (when rateTypeName is not provided)
            else if (
                !rateTypeName &&
                newRates[periodIndex] &&
                newRates[periodIndex].rates[rangeIndex] &&
                newRates[periodIndex].rates[rangeIndex][rateType]
            ) {
                const rateObj = newRates[periodIndex].rates[rangeIndex][rateType][`${rateType}_room_rate`];
                if (rateObj) {
                    if (["BASE_RATE", "ADDITIONAL_PEOPLE", "MIN_STAY_NIGHTS", "ADDITIONAL_PERSON_COST"].includes(field)) {
                        rateObj[field] = parseFloat(value) || 0;
                    } else if (["RATE_UNIT", "CURRENCY"].includes(field)) {
                        rateObj[field] = value;
                    }
                } else if (newRates[periodIndex].rates[rangeIndex][rateType][`${rateType}_rate`]) {
                    newRates[periodIndex].rates[rangeIndex][rateType][`${rateType}_rate`][field] = parseFloat(value) || 0;
                }
            }

            return newRates;
        });
    };

    const handleChildCategoryRateChange = (periodIndex, rangeIndex, rateTypeName, ageRange, field, value) => {
        setEditableRoomRates(prevRates => {
            const newRates = JSON.parse(JSON.stringify(prevRates)); // Deep copy

            if (newRates[periodIndex] &&
                newRates[periodIndex].rates[rangeIndex] &&
                newRates[periodIndex].rates[rangeIndex].child &&
                newRates[periodIndex].rates[rangeIndex].child[rateTypeName] &&
                newRates[periodIndex].rates[rangeIndex].child[rateTypeName].child_rate &&
                newRates[periodIndex].rates[rangeIndex].child[rateTypeName].child_rate.categories &&
                newRates[periodIndex].rates[rangeIndex].child[rateTypeName].child_rate.categories[ageRange]) {

                newRates[periodIndex].rates[rangeIndex].child[rateTypeName].child_rate.categories[ageRange][field] = parseFloat(value) || 0;
            }

            return newRates;
        });
    };
    // Function to map complex search terms to more effective search terms
    const mapSearchTerm = (itemText) => {
        if (!itemText || typeof itemText !== 'string') {
            return itemText;
        }

        const text = itemText.trim();

        // Handle section headers
        if (text === 'Meal Type') return 'meal';
        if (text === 'Minimum Night Stay') return 'minimum night';
        if (text === 'Room Rates') return 'rate';
        if (text === 'Single Room Rate') return 'single';
        if (text === 'Double Room Rate') return 'double';
        if (text === 'Base Capacity') return 'capacity';
        if (text === 'Total Capacity') return 'capacity';
        if (text === 'Max Adults') return 'adult';
        if (text === 'Max Children') return 'child';

        // Handle meal type mappings - extract key terms
        if (text.includes('B&B') || text.includes('Bed & Breakfast')) return 'breakfast';
        if (text.includes('HB') || text.includes('Half Board')) return 'half board';
        if (text.includes('FB') || text.includes('Full Board')) return 'full board';
        if (text.includes('AI') || text.includes('All-Inclusive') || text.includes('All Inclusive')) return 'all inclusive';
        if (text.includes('RO') || text.includes('Room Only')) return 'room only';
        if (text.includes('SC') || text.includes('Self Catering')) return 'self catering';

        // Handle rate-related terms
        if (text === 'Base Rate') return 'rate';
        if (text === 'Additional People Cost') return 'additional';
        if (text === 'Additional People') return 'additional';

        // Handle stay-related terms
        if (text === 'Minimum Nights') return 'minimum night';
        if (text === 'Pay Stay Days') return 'pay stay';
        if (text === 'Pay Stay Free Nights') return 'free night';

        // Handle cancellation terms
        if (text.includes('Cancellation')) return 'cancellation';
        if (text.includes('Fee')) return 'fee';

        // For numeric values that might be too specific, keep them as-is
        // but consider the context
        if (text.match(/^\d+$/) && parseInt(text) < 100) {
            // Small numbers could be nights, capacity, etc. - keep as-is for now
            return text;
        }

        // For currency amounts, keep as-is
        if (text.match(/^R?\d+(\.\d{2})?$/) || text.includes('R')) {
            return text;
        }

        // Default: return the original text
        return text;
    };

    const handleItemClick = (itemText) => {
        const mappedSearchTerm = mapSearchTerm(itemText);
        setSearchText(mappedSearchTerm);
        setSearchStatus('searching'); // Indicate that viewer should start searching
    };

    const handleSearchResult = (status) => {
        setSearchStatus(status); // Update status based on search result from viewer (e.g., 'found', 'not_found')
    };



    const handleCancellationPolicyEdit = () => setEditingCancellationPolicy(true);
    const handleCancellationPolicySave = () => { setEditingCancellationPolicy(false); /* Update logic might be needed if roomTypeAnalysis is the source of truth */ };
    const handleCancellationPolicyCancel = () => { setEditingCancellationPolicy(false); if (roomTypeAnalysis?.cancellationPolicyData?.cancellation_policy?.POLICIES) setEditableCancellationPolicies(JSON.parse(JSON.stringify(roomTypeAnalysis.cancellationPolicyData.cancellation_policy.POLICIES))); else setEditableCancellationPolicies([{ CANCELLATION_FEE: '', START_DAY: '' }]); };
    const handleCancellationPolicyAdd = () => { setEditableCancellationPolicies([...editableCancellationPolicies, { CANCELLATION_FEE: '', START_DAY: '' }]); };
    const handleCancellationPolicyRemove = (index) => { setEditableCancellationPolicies(editableCancellationPolicies.filter((_, i) => i !== index)); };

    const handlePayStayEdit = () => setEditingPayStay(true);
    const handlePayStaySave = () => { setEditingPayStay(false); };
    const handlePayStayCancel = () => { setEditingPayStay(false); if (roomTypeAnalysis?.payStayData?.pay_stays?.OFFERS) setEditablePayStayOffers(JSON.parse(JSON.stringify(roomTypeAnalysis.payStayData.pay_stays.OFFERS))); else setEditablePayStayOffers([{ PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '', PERIODS: [] }]); };
    const handlePayStayAdd = () => { setEditablePayStayOffers([...editablePayStayOffers, { PAY_STAY_DAYS: '', PAY_STAY_FREE_NIGHTS: '', PERIODS: [] }]); };
    const handlePayStayRemove = (index) => { setEditablePayStayOffers(editablePayStayOffers.filter((_, i) => i !== index)); };

    const handleMealTypeEdit = () => setEditingMealType(true);
    const handleMealTypeSave = () => { setEditingMealType(false); };
    const handleMealTypeCancel = () => { setEditingMealType(false); if (roomTypeAnalysis?.mealTypeData) setEditableMealType(roomTypeAnalysis.mealTypeData.meal_type ?? ''); else setEditableMealType(''); };

    const handleCapacityEdit = () => {
        setEditingCapacity(true);
        setPreviousRoomCapacity({ ...editableRoomCapacity });
        setIsUserEditingCapacity(true);
    };

    const handleCapacitySave = () => {
        setIsUserEditingCapacity(false);
        // Always show modal before saving capacity changes
        setPendingCapacity({ ...editableRoomCapacity });
        setShowCapacityDecreaseModal(true);
    };

    const handleCapacityCancel = () => {
        setEditingCapacity(false);
        setIsUserEditingCapacity(false);
        if (roomTypeAnalysis?.roomCapacityData?.capacity) setEditableRoomCapacity(JSON.parse(JSON.stringify(roomTypeAnalysis.roomCapacityData.capacity)));
        else setEditableRoomCapacity({ BASE_CAPACITY: '', TOTAL_CAPACITY: '', MAX_ADULTS: '', MAX_CHILDREN: '' });
    };

    // Modal handlers
    const handleCapacityDecreaseAccept = () => {
        setShowCapacityDecreaseModal(false);
        setEditingCapacity(false);
        if (pendingCapacity) {
            setEditableRoomCapacity(pendingCapacity);
            refetchRatesAfterCapacityChange(pendingCapacity);
        }
        setPendingCapacity(null);
    };
    const handleCapacityDecreaseReject = () => {
        setShowCapacityDecreaseModal(false);
        setEditingCapacity(false);
        // Revert to previous
        if (previousRoomCapacity) setEditableRoomCapacity(previousRoomCapacity);
        setPendingCapacity(null);
    };

    // Helper to re-fetch rates after capacity change
    const refetchRatesAfterCapacityChange = (newCapacity) => {
        if (selectedRoomType && selectedProperty && workflowState && workflowState.documentFilename && periods && groupedPeriods) {
            fetchRoomTypeAnalysis(
                selectedRoomType,
                selectedProperty,
                workflowState.documentFilename,
                periods,
                groupedPeriods,
                newCapacity // Pass the override
            );
        }
    };

    const handleRatesEdit = () => setEditingRates(true);
    const handleRatesSave = () => { setEditingRates(false); };
    const handleRatesCancel = () => {
        setEditingRates(false);
        if (roomTypeAnalysis?.rates && roomTypeAnalysis.rates.length > 0) {
            setEditableRoomRates(JSON.parse(JSON.stringify(roomTypeAnalysis.rates)));
        } else if (initialGroupedPeriods.length > 0) {
            // Create the same structure as in fetchRoomTypeAnalysis
            const createEmptyRateStructure = () => {
                const structure = {};
                ['single', 'double', 'triple', 'quad', 'child', 'infant'].forEach(roomRateType => {
                    structure[roomRateType] = {};
                    rateTypes.forEach(rateType => {
                        structure[roomRateType][rateType] = null;
                    });
                });
                return structure;
            };

            const reconstructedInitialRates = initialGroupedPeriods.map(period => ({
                period: period.name,
                dateRanges: period.dateRanges,
                rates: period.dateRanges.map(() => createEmptyRateStructure())
            }));
            setEditableRoomRates(reconstructedInitialRates);
        } else {
            setEditableRoomRates([]);
        }
    };
    // Helper function to safely extract value from potentially malformed data
    const safeExtractValue = (value, defaultValue = null) => {
        // If value is an object with 'desc' and 'type' properties, it's a schema definition, not actual data
        if (value && typeof value === 'object' && value.hasOwnProperty('desc') && value.hasOwnProperty('type')) {
            console.warn('Encountered schema definition instead of actual value:', value);
            return defaultValue;
        }
        return value !== undefined && value !== null ? value : defaultValue;
    };

    // Helper function to safely render values in UI (prevents React object rendering errors)
    const safeRenderValue = (value, defaultValue = 'N/A') => {
        // If value is an object with 'desc' and 'type' properties, it's a schema definition
        if (value && typeof value === 'object' && value.hasOwnProperty('desc') && value.hasOwnProperty('type')) {
            console.warn('Attempted to render schema definition in UI:', value);
            return defaultValue;
        }
        // If it's any other object, don't render it
        if (value && typeof value === 'object') {
            console.warn('Attempted to render object in UI:', value);
            return defaultValue;
        }
        return value !== undefined && value !== null && value !== '' ? value : defaultValue;
    };

    // Helper function to convert new format to old format for compatibility
    const convertNewFormatToOld = (newRateData, rateType) => {
        // Handle the actual API response structure: { double_room_rate: { room_rate_package: {...} } }
        const rateKey = `${rateType}_room_rate`;
        if (!newRateData || !newRateData[rateKey] || !newRateData[rateKey].room_rate_package) {
            return newRateData; // Return as-is if not new format
        }

        const pkg = newRateData[rateKey].room_rate_package;

        // Convert to old format structure with safe value extraction
        const oldFormat = {
            [`${rateType}_room_rate`]: {
                BASE_RATE: safeExtractValue(pkg.rate_amount, 0),
                ADDITIONAL_PEOPLE: safeExtractValue(pkg.total_capacity, 0) - safeExtractValue(pkg.base_capacity, 0), // Number of additional people allowed
                RATE_UNIT: safeExtractValue(pkg.rate_unit, 'room_per_night'),
                CURRENCY: safeExtractValue(pkg.currency, 'ZAR'),
                BASE_CAPACITY: safeExtractValue(pkg.base_capacity, 0),
                TOTAL_CAPACITY: safeExtractValue(pkg.total_capacity, 0),
                MIN_STAY_NIGHTS: safeExtractValue(pkg.min_stay_nights, 0),
                ADDITIONAL_FEE_RULES: Array.isArray(pkg.additional_fee_rules) ? pkg.additional_fee_rules : [],
                IS_AVAILABLE: safeExtractValue(pkg.is_available, false),
                DATE_FROM: safeExtractValue(pkg.date_from, ''),
                DATE_TO: safeExtractValue(pkg.date_to, ''),
                // Store calculated additional costs separately for display
                ADDITIONAL_PERSON_COST: 0,
                FLAT_FEE_COST: 0
            }
        };

        // Calculate additional costs from fee rules
        if (pkg.additional_fee_rules && Array.isArray(pkg.additional_fee_rules)) {
            let additionalPersonCost = 0;
            let flatFeeCost = 0;

            pkg.additional_fee_rules.forEach(rule => {
                if (rule.fee_amount) {
                    if (rule.fee_unit === 'per_person_per_night' || rule.fee_unit === 'per_additional_adult') {
                        additionalPersonCost += rule.fee_amount;
                    } else if (rule.fee_unit === 'flat_fee') {
                        flatFeeCost += rule.fee_amount;
                    }
                }
            });

            oldFormat[`${rateType}_room_rate`].ADDITIONAL_PERSON_COST = additionalPersonCost;
            oldFormat[`${rateType}_room_rate`].FLAT_FEE_COST = flatFeeCost;
        }

        return oldFormat;
    };

    // Helper function to convert new format to old format for child/infant rates
    const convertChildInfantFormatToOld = (newRateData, rateType) => {
        // Handle simple integer format: { child_rate: 350 } or { child_rate: -1 }
        const rateKey = `${rateType}_rate`;
        if (!newRateData || typeof newRateData[rateKey] === 'undefined') {
            return newRateData; // Return as-is if not found
        }

        // If it's already a simple number, convert to old structure
        if (typeof newRateData[rateKey] === 'number') {
            const rateValue = newRateData[rateKey] === -1 ? 0 : newRateData[rateKey]; // Convert -1 to 0
            return {
                [`${rateType}_rate`]: {
                    RATE: rateValue,
                    RATE_UNIT: 'per_person_per_night',
                    CURRENCY: 'ZAR',
                    ADDITIONAL_FEE_RULES: []
                }
            };
        }

        // If it has room_rate_package structure, handle that
        if (newRateData[rateKey] && newRateData[rateKey].room_rate_package) {
            const pkg = newRateData[rateKey].room_rate_package;
            return {
                [`${rateType}_rate`]: {
                    RATE: safeExtractValue(pkg.rate_amount, 0),
                    RATE_UNIT: safeExtractValue(pkg.rate_unit, 'per_person_per_night'),
                    CURRENCY: safeExtractValue(pkg.currency, 'ZAR'),
                    ADDITIONAL_FEE_RULES: Array.isArray(pkg.additional_fee_rules) ? pkg.additional_fee_rules : []
                }
            };
        }

        return newRateData; // Return as-is if unknown format
    };

    // Helper function to format currency display
    const formatCurrency = (currency) => {
        const safeCurrency = safeRenderValue(currency, 'ZAR');
        return safeCurrency === 'ZAR' ? 'R' : safeCurrency;
    };

    // Helper function to format rate unit display
    const formatRateUnit = (rateUnit) => {
        const safeRateUnit = safeRenderValue(rateUnit, '');
        if (!safeRateUnit || typeof safeRateUnit !== 'string') return '';
        return safeRateUnit.replace(/_/g, ' ');
    };

    // Helper function to calculate total room cost based on rate unit
    const calculateRoomTotalCost = (rateInfo) => {
        if (!rateInfo) return 0;

        const baseRate = rateInfo.BASE_RATE || 0;
        const additionalPeople = rateInfo.ADDITIONAL_PEOPLE || 0; // Number of additional people
        const additionalPersonCost = rateInfo.ADDITIONAL_PERSON_COST || 0; // Cost per additional person
        const flatFeeCost = rateInfo.FLAT_FEE_COST || 0; // Flat fee cost
        const baseCapacity = rateInfo.BASE_CAPACITY || 1;
        const rateUnit = rateInfo.RATE_UNIT || 'room_per_night';

        let totalCost = 0;

        if (rateUnit === 'person_per_night') {
            // Base rate applies per person for base capacity
            totalCost = baseRate * baseCapacity;
        } else {
            // Base rate is per room
            totalCost = baseRate;
        }

        // Add cost for additional people (if any)
        if (additionalPeople > 0 && additionalPersonCost > 0) {
            totalCost += (additionalPeople * additionalPersonCost);
        }

        // Add flat fee cost
        totalCost += flatFeeCost;

        return totalCost;
    };

    // Helper function to convert new children rates format to old format for compatibility
    const convertChildrenRatesFormatToOld = (newRateData) => {
        // Handle the new format: {child_rates: { "0-3": {...}, "4-11": {...} } }
        if (!newRateData || !newRateData.child_rates) {
            return newRateData; // Return as-is if not found
        }

        const childRates = newRateData.child_rates;

        // Process each category to handle malformed data
        const processedCategories = {};
        Object.keys(childRates).forEach(ageRange => {
            const categoryData = childRates[ageRange] > 0 ? childRates[ageRange] : 0;
            
            processedCategories[ageRange] = {
                rate_amount: categoryData,
                rate_unit: safeExtractValue(categoryData.rate_unit, 'per_person_per_night'),
                currency: safeExtractValue(categoryData.currency, 'R'),
                additional_fee_rules: Array.isArray(categoryData.additional_fee_rules) ? categoryData.additional_fee_rules : []
            };
        });

        // Convert to old structure with child_rate containing multiple categories
        const firstCategory = Object.values(processedCategories)[0];
        return {
            child_rate: {
                categories: processedCategories,
                // For backward compatibility, also include the first category as the main rate
                RATE: firstCategory?.rate_amount || 0,
                RATE_UNIT: firstCategory?.rate_unit || 'per_person_per_night',
                CURRENCY: firstCategory?.currency || 'ZAR',
                ADDITIONAL_FEE_RULES: []
            }
        };
    };

    const renderSearchStatusIcon = (itemText) => {
        if (searchText === itemText) {
            if (searchStatus === 'searching') {
                return <span className="ml-2 animate-spin">⏳</span>; // Spinner
            } else if (searchStatus === 'found') {
                return <span className="ml-2 text-green-500">✔️</span>; // Checkmark
            } else if (searchStatus === 'not_found') {
                return <span className="ml-2 text-red-500">❌</span>; // Cross
            }
        }
        return null;
    };
    

    return (
        <div className="min-h-screen bg-gray-100 py-6">
            <div className="container mx-auto px-4">
                <div className="flex justify-between items-center mb-8">
                    <h1 className="text-3xl font-bold">Room Type Analysis</h1>
                    <button
                        onClick={handleBackNavigation}
                        className="px-4 py-2 text-blue-500 hover:text-blue-700"
                    >
                        ← Back
                    </button>
                </div>
                
                {error && (
                    <div className="mb-8 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
                        {error}
                    </div>
                )}
                
                {/* Add Model Selector */}
                <div className="mb-3">
                    <label htmlFor="modelSelect" className="form-label">Select Model:</label>
                    <select 
                        id="modelSelect" 
                        className="form-select" 
                        value={selectedModel} 
                        onChange={(e) => {
                            setSelectedModel(e.target.value);
                            localStorage.setItem('selectedModel', e.target.value);
                        }}
                    >
                        {modelOptions.map(option => (
                            <option key={option.value} value={option.value}>
                                {option.label}
                            </option>
                        ))}
                    </select>
                </div>
                
                {/* Main Content Area - Two Column Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 items-start">
                    {/* Left Column - Room Type Selection and Analysis */}
                    <div className="lg:col-span-2 flex flex-col gap-6">               
                        {/* Progress Display */}
                        {workflowState && selectedProperty && roomTypeList.length > 0 && (
                            <div className="bg-white p-4 rounded-lg shadow-md flex-grow">
                                <h2 className="text-xl font-semibold mb-2 text-gray-800">
                                    Property: <span className="font-bold">{selectedProperty}</span>
                                </h2>
                                <p className="text-md font-medium text-blue-700">
                                    Analyzing Room Type {currentRoomTypeIndex + 1} / {roomTypeList.length}:
                                    <span onClick={() => handleItemClick(selectedRoomType)} className="ml-2 font-bold cursor-pointer hover:underline">{selectedRoomType}</span>
                                    {renderSearchStatusIcon(selectedRoomType)}
                                </p>
                                {workflowState && typeof workflowState.currentPropertyIndex === 'number' && typeof workflowState.totalProperties === 'number' && // Ensure properties exist and are numbers
                                    <p className="text-sm text-gray-600">
                                        (Property {workflowState.currentPropertyIndex + 1} / {workflowState.totalProperties} in workflow)
                                    </p>
                                }
                            </div>
                        )}
                        {/* Room Type Analysis Results */}
                        <div className="bg-white p-6 rounded-lg shadow-md flex-grow">
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-xl font-semibold">Analysis Results for {selectedRoomType}</h2>
                                {/* Placeholder for future individual edit buttons if needed at top level */}
                            </div>
                            {/* ... Loading indicator ... */}
                            
                            {loading && (
                                <div className="flex justify-center items-center h-40">
                                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                                </div>
                            )}
                            
                            {roomTypeAnalysis && (
                                <div className="space-y-6">


                                    {/* Cancellation Policy Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Cancellation Policy')} className="text-lg font-medium text-red-600 mb-2 cursor-pointer hover:underline">Cancellation Policy</h3>
                                        <div className="bg-red-50 p-4 rounded-lg">
                                            {editingCancellationPolicy ? (
                                                <React.Fragment>
                                                    {editableCancellationPolicies.map((policy, index) => (
                                                        <div key={index} className="bg-white p-3 rounded shadow-sm space-y-2 mb-3">
                                                                <div>
                                                                    <label htmlFor={`cancelFee-${index}`} className="block text-sm font-medium text-gray-700">
                                                                        Cancellation Fee (% or Amount):
                                                                    </label>
                                                                    <input
                                                                        type="text"
                                                                        id={`cancelFee-${index}`}
                                                                        value={policy.CANCELLATION_FEE}
                                                                        onChange={(e) => handleCancellationPolicyChange(index, 'CANCELLATION_FEE', e.target.value)}
                                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                                        placeholder="e.g., 50% or 100"
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <label htmlFor={`startDay-${index}`} className="block text-sm font-medium text-gray-700">
                                                                        Start Day (days before check-in):
                                                                    </label>
                                                                    <input
                                                                        type="number"
                                                                        id={`startDay-${index}`}
                                                                        value={policy.START_DAY}
                                                                        onChange={(e) => handleCancellationPolicyChange(index, 'START_DAY', e.target.value)}
                                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                                        placeholder="e.g., 14"
                                                                    />
                                                                </div>
                                                            <div className="flex justify-end">
                                                                <button onClick={() => handleCancellationPolicyRemove(index)} className="px-3 py-1 text-xs text-red-500 hover:text-red-700 border border-red-300 rounded-md hover:bg-red-50">Remove</button>
                                                            </div>
                                                        </div>
                                                    ))}
                                                    <button onClick={handleCancellationPolicyAdd} className="mt-2 px-4 py-2 text-sm text-white bg-blue-500 hover:bg-blue-600 rounded-lg">Add Policy</button>
                                                    <div className="flex justify-end gap-2 mt-3">
                                                        <button onClick={handleCancellationPolicyCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                        <button onClick={handleCancellationPolicySave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {editableCancellationPolicies.map((policy, index) => (
                                                        <div key={index} className="bg-white p-3 rounded shadow-sm space-y-2 mb-2">
                                                            <p><strong onClick={() => handleItemClick('Cancellation Fee')} className="cursor-pointer hover:underline">Cancellation Fee:</strong> <span onClick={() => handleItemClick(String(policy.CANCELLATION_FEE || ''))} className="cursor-pointer hover:underline">{policy.CANCELLATION_FEE !== null && policy.CANCELLATION_FEE !== '' ? policy.CANCELLATION_FEE : 'N/A'}</span>{renderSearchStatusIcon(String(policy.CANCELLATION_FEE || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Start Day')} className="cursor-pointer hover:underline">Start Day:</strong> <span onClick={() => handleItemClick(String(policy.START_DAY || ''))} className="cursor-pointer hover:underline">{policy.START_DAY !== null && policy.START_DAY !== '' ? policy.START_DAY : 'N/A'}</span>{renderSearchStatusIcon(String(policy.START_DAY || ''))}</p>
                                                        </div>
                                                    ))}
                                                    {roomTypeAnalysis && roomTypeAnalysis.cancellationPolicyData && roomTypeAnalysis.cancellationPolicyData.error && !editingCancellationPolicy && (
                                                        <p className="text-red-500 text-xs italic"><strong>Error:</strong> {roomTypeAnalysis.cancellationPolicyData.error}</p>
                                                    )}
                                                    <div className="flex justify-end mt-4">
                                                        <button onClick={handleCancellationPolicyEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Cancellation Policy</button>
                                                    </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Pay Stay Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Pay Stay Offers')} className="text-lg font-medium text-green-600 mb-2 cursor-pointer hover:underline">Pay Stay Offers</h3>
                                        <div className="bg-green-50 p-4 rounded-lg">
                                            {editingPayStay ? (
                                                <React.Fragment>
                                                    {editablePayStayOffers.map((offer, index) => (
                                                        <div key={index} className="bg-white p-3 rounded shadow-sm space-y-2 mb-3">
                                                                <div>
                                                                    <label htmlFor={`payDays-${index}`} className="block text-sm font-medium text-gray-700">
                                                                        Pay Stay Days:
                                                                    </label>
                                                                    <input
                                                                        type="number"
                                                                        id={`payDays-${index}`}
                                                                        value={offer.PAY_STAY_DAYS}
                                                                        onChange={(e) => handlePayStayChange(index, 'PAY_STAY_DAYS', e.target.value)}
                                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                                        placeholder="e.g., 7"
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <label htmlFor={`freeNights-${index}`} className="block text-sm font-medium text-gray-700">
                                                                        Pay Stay Free Nights:
                                                                    </label>
                                                                    <input
                                                                        type="number"
                                                                        id={`freeNights-${index}`}
                                                                        value={offer.PAY_STAY_FREE_NIGHTS}
                                                                        onChange={(e) => handlePayStayChange(index, 'PAY_STAY_FREE_NIGHTS', e.target.value)}
                                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                                        placeholder="e.g., 1"
                                                                    />
                                                                </div>
                                                            <div className="flex justify-end">
                                                                <button onClick={() => handlePayStayRemove(index)} className="px-3 py-1 text-xs text-red-500 hover:text-red-700 border border-red-300 rounded-md hover:bg-red-50">Remove</button>
                                                            </div>
                                                        </div>
                                                    ))}
                                                    <button onClick={handlePayStayAdd} className="mt-2 px-4 py-2 text-sm text-white bg-blue-500 hover:bg-blue-600 rounded-lg">Add Offer</button>
                                                    <div className="flex justify-end gap-2 mt-3">
                                                        <button onClick={handlePayStayCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                        <button onClick={handlePayStaySave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    {editablePayStayOffers.map((offer, index) => (
                                                        <div key={index} className="bg-white p-3 rounded shadow-sm space-y-2 mb-2">
                                                            <p><strong onClick={() => handleItemClick('Pay Stay Days')} className="cursor-pointer hover:underline">Pay Stay Days:</strong> <span onClick={() => handleItemClick(String(offer.PAY_STAY_DAYS || ''))} className="cursor-pointer hover:underline">{offer.PAY_STAY_DAYS !== null && offer.PAY_STAY_DAYS !== '' ? offer.PAY_STAY_DAYS : 'N/A'}</span>{renderSearchStatusIcon(String(offer.PAY_STAY_DAYS || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Pay Stay Free Nights')} className="cursor-pointer hover:underline">Pay Stay Free Nights:</strong> <span onClick={() => handleItemClick(String(offer.PAY_STAY_FREE_NIGHTS || ''))} className="cursor-pointer hover:underline">{offer.PAY_STAY_FREE_NIGHTS !== null && offer.PAY_STAY_FREE_NIGHTS !== '' ? offer.PAY_STAY_FREE_NIGHTS : 'N/A'}</span>{renderSearchStatusIcon(String(offer.PAY_STAY_FREE_NIGHTS || ''))}</p>
                                                            {offer.PERIODS && offer.PERIODS.length > 0 && (
                                                                <div>
                                                                    <p><strong onClick={() => handleItemClick('Pay Stay Periods')} className="cursor-pointer hover:underline">Applicable Periods:</strong></p>
                                                                    <div className="ml-4 space-y-1">
                                                                        {offer.PERIODS.map((period, periodIndex) => (
                                                                            <p key={periodIndex} className="text-sm">
                                                                                <span onClick={() => handleItemClick(period[0])} className="cursor-pointer hover:underline font-medium">{period[0]}</span>:
                                                                                <span onClick={() => handleItemClick(period[1])} className="cursor-pointer hover:underline"> {period[1]}</span> -
                                                                                <span onClick={() => handleItemClick(period[2])} className="cursor-pointer hover:underline">{period[2]}</span>
                                                                                {renderSearchStatusIcon(period[0])}
                                                                            </p>
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>
                                                    ))}
                                                    {roomTypeAnalysis && roomTypeAnalysis.payStayData && roomTypeAnalysis.payStayData.error && !editingPayStay && (
                                                        <p className="text-red-500 text-xs italic"><strong>Error:</strong> {roomTypeAnalysis.payStayData.error}</p>
                                                    )}
                                                    <div className="flex justify-end mt-4">
                                                        <button onClick={handlePayStayEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Pay Stay Offers</button>
                                                    </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                                    {/* Meal Type Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Meal Type')} className="text-lg font-medium text-orange-600 mb-2 cursor-pointer hover:underline">Meal Type</h3>
                                        <div className="bg-orange-50 p-4 rounded-lg">
                                            {editingMealType ? (
                                                <React.Fragment>
                                                    <label htmlFor="mealTypeInput" className="block text-sm font-medium text-gray-700">
                                                        Meal Type:
                                                    </label>
                                                    <input
                                                        type="text"
                                                        id="mealTypeInput"
                                                        value={editableMealType}
                                                        onChange={(e) => setEditableMealType(e.target.value)}
                                                        className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                        placeholder="e.g., Bed & Breakfast"
                                                    />
                                                    <div className="flex justify-end gap-2 mt-3">
                                                        <button onClick={handleMealTypeCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                        <button onClick={handleMealTypeSave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                    </div>
                                                </React.Fragment>
                                            ) : (
                                                <React.Fragment>
                                                    <p><strong onClick={() => handleItemClick('Meal Type')} className="cursor-pointer hover:underline">Meal Type:</strong> <span onClick={() => handleItemClick(String(editableMealType || ''))} className="cursor-pointer hover:underline">{editableMealType !== null && editableMealType !== '' ? editableMealType : 'N/A'}</span>{renderSearchStatusIcon(String(editableMealType || ''))}</p>
                                                    {roomTypeAnalysis && roomTypeAnalysis.mealTypeData && roomTypeAnalysis.mealTypeData.error && !editingMealType && (
                                                        <p className="text-red-500 text-xs italic"><strong>Error:</strong> {roomTypeAnalysis.mealTypeData.error}</p>
                                                    )}
                                                    <div className="flex justify-end mt-4">
                                                        <button onClick={handleMealTypeEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Meal Type</button>
                                                    </div>
                                                </React.Fragment>
                                            )}
                                        </div>
                                    </div>

                

                                    {/* Room Rates Section */}
                                    <div>
                                        <h3 onClick={() => handleItemClick('Room Rates')} className="text-lg font-medium text-purple-600 mb-2 cursor-pointer hover:underline">Room Rates</h3>
                                        <div className="bg-purple-50 p-4 rounded-lg">
                                            {/* Editable Room Capacity Section */}
                                            <div className="bg-white p-3 rounded mb-4 shadow-sm">
                                                <h5 onClick={() => handleItemClick('Room Capacity')} className="text-md font-medium text-gray-800 mb-2 cursor-pointer hover:underline">Room Capacity</h5>
                                                {editingCapacity && !editingRates ? (
                                                    <React.Fragment>
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label htmlFor="baseCapacity" className="block text-sm font-medium text-gray-700">Base Capacity:</label>
                                                                <input type="number" id="baseCapacity" value={editableRoomCapacity.BASE_CAPACITY} onChange={(e) => handleRoomCapacityChange('BASE_CAPACITY', e.target.value)} className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., 2"/>
                                                            </div>
                                                            <div>
                                                                <label htmlFor="totalCapacity" className="block text-sm font-medium text-gray-700">Total Capacity (Max A+C):</label>
                                                                <input type="number" id="totalCapacity" value={editableRoomCapacity.TOTAL_CAPACITY} onChange={(e) => handleRoomCapacityChange('TOTAL_CAPACITY', e.target.value)} className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., 4"/>
                                                            </div>
                                                            <div>
                                                                <label htmlFor="maxAdults" className="block text-sm font-medium text-gray-700">Max Adults:</label>
                                                                <input type="number" id="maxAdults" value={editableRoomCapacity.MAX_ADULTS} onChange={(e) => handleRoomCapacityChange('MAX_ADULTS', e.target.value)} className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., 2"/>
                                                            </div>
                                                            <div>
                                                                <label htmlFor="maxChildren" className="block text-sm font-medium text-gray-700">Max Children:</label>
                                                                <input type="number" id="maxChildren" value={editableRoomCapacity.MAX_CHILDREN} onChange={(e) => handleRoomCapacityChange('MAX_CHILDREN', e.target.value)} className="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="e.g., 2"/>
                                                            </div>
                                                        </div>
                                                        <div className="flex justify-end gap-2 mt-3">
                                                            <button onClick={handleCapacityCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel</button>
                                                            <button onClick={handleCapacitySave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save</button>
                                                        </div>
                                                    </React.Fragment>
                                                ) : (
                                                    <React.Fragment>
                                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <p><strong onClick={() => handleItemClick('Base Capacity')} className="cursor-pointer hover:underline">Base Capacity:</strong> <span onClick={() => handleItemClick(String(editableRoomCapacity.BASE_CAPACITY || ''))} className="cursor-pointer hover:underline">{editableRoomCapacity.BASE_CAPACITY !== null && editableRoomCapacity.BASE_CAPACITY !== '' ? editableRoomCapacity.BASE_CAPACITY : 'N/A'}</span> people{renderSearchStatusIcon(String(editableRoomCapacity.BASE_CAPACITY || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Total Capacity')} className="cursor-pointer hover:underline">Total Capacity (Max A+C):</strong> <span onClick={() => handleItemClick(String(editableRoomCapacity.TOTAL_CAPACITY || ''))} className="cursor-pointer hover:underline">{editableRoomCapacity.TOTAL_CAPACITY !== null && editableRoomCapacity.TOTAL_CAPACITY !== '' ? editableRoomCapacity.TOTAL_CAPACITY : 'N/A'}</span> people{renderSearchStatusIcon(String(editableRoomCapacity.TOTAL_CAPACITY || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Max Adults')} className="cursor-pointer hover:underline">Max Adults:</strong> <span onClick={() => handleItemClick(String(editableRoomCapacity.MAX_ADULTS || ''))} className="cursor-pointer hover:underline">{editableRoomCapacity.MAX_ADULTS !== null && editableRoomCapacity.MAX_ADULTS !== '' ? editableRoomCapacity.MAX_ADULTS : 'N/A'}</span>{renderSearchStatusIcon(String(editableRoomCapacity.MAX_ADULTS || ''))}</p>
                                                            <p><strong onClick={() => handleItemClick('Max Children')} className="cursor-pointer hover:underline">Max Children:</strong> <span onClick={() => handleItemClick(String(editableRoomCapacity.MAX_CHILDREN || ''))} className="cursor-pointer hover:underline">{editableRoomCapacity.MAX_CHILDREN !== null && editableRoomCapacity.MAX_CHILDREN !== '' ? editableRoomCapacity.MAX_CHILDREN : 'N/A'}</span>{renderSearchStatusIcon(String(editableRoomCapacity.MAX_CHILDREN || ''))}</p>
                                                        </div>
                                                        <div className="flex justify-end mt-4">
                                                            <button onClick={handleCapacityEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Capacity</button>
                                                        </div>
                                                    </React.Fragment>
                                                )}
                                                {roomTypeAnalysis && roomTypeAnalysis.roomCapacityData && roomTypeAnalysis.roomCapacityData.error && !editingCapacity && !editingRates && (
                                                    <p className="text-red-500 text-xs italic mt-2"><strong>Error:</strong> {roomTypeAnalysis.roomCapacityData.error}</p>
                                                )}
                                            </div>

                                            {editingRates && (
                                                <div className="flex justify-end gap-2 mb-3 mt-4">  {/* Save/Cancel for Rates at top of rates list */}
                                                    <button onClick={handleRatesCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel Rates</button>
                                                    <button onClick={handleRatesSave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save Rates</button>
                                                </div>
                                            )}
                                            {roomTypeAnalysis.rates && roomTypeAnalysis.rates.length > 0 ? (
                                                <div className="space-y-6">
                                                    {editableRoomRates.map((periodData, periodIndex) => (
                                                        <div key={periodIndex} className="border-b border-purple-200 pb-4 last:border-0">
                                                            <div className="flex justify-between items-center mb-3">
                                                                <h4 className="text-lg font-semibold">{periodData.period}</h4>
                                                                {!editingRates && (
                                                                    <button onClick={handleRatesEdit} className="px-3 py-1 text-sm text-blue-500 hover:text-blue-700 border border-blue-300 rounded hover:bg-blue-50">
                                                                        Edit Rates
                                                                    </button>
                                                                )}
                                                                {editingRates && (
                                                                    <div className="flex gap-2">
                                                                        <button onClick={handleRatesCancel} className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-100">Cancel</button>
                                                                        <button onClick={handleRatesSave} className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600">Save</button>
                                                                    </div>
                                                                )}
                                                            </div>
                                                            
                                                            {periodData.dateRanges.map((dateRange, rangeIndex) => {
                                                                const currentRangeRates = periodData.rates[rangeIndex]; // This is from editableRoomRates
                                                                return (
                                                                    <div key={rangeIndex} className="mb-4 last:mb-0">
                                                                        <div className="text-sm text-gray-600 mb-2">
                                                                            {formatDateToDDMMYYYY(dateRange.start)} - {formatDateToDDMMYYYY(dateRange.end)}
                                                                        </div>
                                                                        
                                                                        {currentRangeRates.error ? (
                                                                            <p className="text-red-600 text-sm">{currentRangeRates.error}</p>
                                                                        ) : (
                                                                            <div className="space-y-4">
                                                                                {/* Single Room Rate - Display all rate types */}
                                                                                {currentRangeRates.single && typeof currentRangeRates.single === 'object' && Object.keys(currentRangeRates.single).length > 0 && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Single Room Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Single Room Rate</h5>
                                                                                        <div className="space-y-3">
                                                                                            {Object.entries(currentRangeRates.single).map(([rateTypeName, rateData]) => {
                                                                                                if (!rateData || !rateData.single_room_rate) return null;
                                                                                                return (
                                                                                                    <div key={rateTypeName} className="border-l-4 border-blue-200 pl-3">
                                                                                                        <h6 className="font-medium text-blue-700 mb-1">{rateTypeName}</h6>
                                                                                                        {editingRates ? (
                                                                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                                                                                                                <div>
                                                                                                                    <label htmlFor={`singleBaseRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Rate Amount ({formatCurrency(rateData.single_room_rate.CURRENCY || 'ZAR')}):</label>
                                                                                                                    <input type="number" id={`singleBaseRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={safeRenderValue(rateData.single_room_rate.BASE_RATE, '')} onChange={e => handleRateChange(periodIndex, rangeIndex, 'single', 'BASE_RATE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`singleRateUnit-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Rate Unit:</label>
                                                                                                                    <select id={`singleRateUnit-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.single_room_rate.RATE_UNIT || 'room_per_night'} onChange={e => handleRateChange(periodIndex, rangeIndex, 'single', 'RATE_UNIT', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                                                    <option value="room_per_night">Room per night</option>
                                                                                    <option value="person_per_night">Person per night</option>
                                                                                </select>
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`singleCurrency-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Currency:</label>
                                                                                                                    <select id={`singleCurrency-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.single_room_rate.CURRENCY || 'ZAR'} onChange={e => handleRateChange(periodIndex, rangeIndex, 'single', 'CURRENCY', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                                                    <option value="ZAR">ZAR</option>
                                                                                    <option value="USD">USD</option>
                                                                                </select>
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`singleMinStay-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Min Stay Nights:</label>
                                                                                                                    <input type="number" id={`singleMinStay-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.single_room_rate.MIN_STAY_NIGHTS || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'single', 'MIN_STAY_NIGHTS', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`singleAdditional-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Additional People (count):</label>
                                                                                                                    <input type="number" id={`singleAdditional-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={safeRenderValue(rateData.single_room_rate.ADDITIONAL_PEOPLE, '')} onChange={e => handleRateChange(periodIndex, rangeIndex, 'single', 'ADDITIONAL_PEOPLE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`singleAdditionalPersonCost-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Cost per Additional Person:</label>
                                                                                                                    <input type="number" id={`singleAdditionalPersonCost-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.single_room_rate.ADDITIONAL_PERSON_COST || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'single', 'ADDITIONAL_PERSON_COST', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div className="md:col-span-2">
                                                                                                                    <p className="text-xs text-gray-600 mb-1"><strong>Base Capacity:</strong> {safeRenderValue(rateData.single_room_rate.BASE_CAPACITY)} people</p>
                                                                                                                    <p className="font-semibold text-green-600 text-sm"><strong>Total Cost:</strong> {formatCurrency(rateData.single_room_rate.CURRENCY || 'ZAR')}{calculateRoomTotalCost(rateData.single_room_rate)}</p>
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        ) : (
                                                                                                            <div className="space-y-1">
                                                                                                                <p><strong onClick={() => handleItemClick('Single')} className="cursor-pointer hover:underline">Base Rate:</strong> {formatCurrency(safeRenderValue(rateData.single_room_rate.CURRENCY, 'ZAR'))}<span onClick={() => handleItemClick(String(safeRenderValue(rateData.single_room_rate.BASE_RATE, '')))} className="cursor-pointer hover:underline">{safeRenderValue(rateData.single_room_rate.BASE_RATE)}</span>{renderSearchStatusIcon(String(safeRenderValue(rateData.single_room_rate.BASE_RATE, '')))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Rate Unit')} className="cursor-pointer hover:underline">Rate Unit:</strong> <span onClick={() => handleItemClick(String(safeRenderValue(rateData.single_room_rate.RATE_UNIT, '')))} className="cursor-pointer hover:underline">{formatRateUnit(safeRenderValue(rateData.single_room_rate.RATE_UNIT, 'room per night'))}</span>{renderSearchStatusIcon(String(safeRenderValue(rateData.single_room_rate.RATE_UNIT, '')))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Base Capacity')} className="cursor-pointer hover:underline">Base Capacity:</strong> <span onClick={() => handleItemClick(String(safeRenderValue(rateData.single_room_rate.BASE_CAPACITY, '')))} className="cursor-pointer hover:underline">{safeRenderValue(rateData.single_room_rate.BASE_CAPACITY)}</span> people{renderSearchStatusIcon(String(safeRenderValue(rateData.single_room_rate.BASE_CAPACITY, '')))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Min Stay Nights')} className="cursor-pointer hover:underline">Min Stay Nights:</strong> <span onClick={() => handleItemClick(String(safeRenderValue(rateData.single_room_rate.MIN_STAY_NIGHTS, '')))} className="cursor-pointer hover:underline">{safeRenderValue(rateData.single_room_rate.MIN_STAY_NIGHTS)}</span>{renderSearchStatusIcon(String(safeRenderValue(rateData.single_room_rate.MIN_STAY_NIGHTS, '')))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Additional People')} className="cursor-pointer hover:underline">Additional People:</strong> <span onClick={() => handleItemClick(String(safeRenderValue(rateData.single_room_rate.ADDITIONAL_PEOPLE, '')))} className="cursor-pointer hover:underline">{safeRenderValue(rateData.single_room_rate.ADDITIONAL_PEOPLE)}</span> people{renderSearchStatusIcon(String(safeRenderValue(rateData.single_room_rate.ADDITIONAL_PEOPLE, '0')))}
                                                                                                                {(rateData.single_room_rate.ADDITIONAL_PERSON_COST > 0 || rateData.single_room_rate.FLAT_FEE_COST > 0) && (
                                                                                                                    <div className="ml-4 text-xs text-gray-600">
                                                                                                                        {rateData.single_room_rate.ADDITIONAL_PERSON_COST > 0 && (
                                                                                                                            <p><strong onClick={() => handleItemClick('Additional Person Cost')} className="cursor-pointer hover:underline">Per Additional Person:</strong> {formatCurrency(safeRenderValue(rateData.single_room_rate.CURRENCY, 'ZAR'))}<span onClick={() => handleItemClick(String(rateData.single_room_rate.ADDITIONAL_PERSON_COST))} className="cursor-pointer hover:underline">{rateData.single_room_rate.ADDITIONAL_PERSON_COST}</span>{renderSearchStatusIcon(String(rateData.single_room_rate.ADDITIONAL_PERSON_COST))}</p>
                                                                                                                        )}
                                                                                                                        {rateData.single_room_rate.FLAT_FEE_COST > 0 && (
                                                                                                                            <p><strong onClick={() => handleItemClick('Flat Fee')} className="cursor-pointer hover:underline">Flat Fee:</strong> {formatCurrency(safeRenderValue(rateData.single_room_rate.CURRENCY, 'ZAR'))}<span onClick={() => handleItemClick(String(rateData.single_room_rate.FLAT_FEE_COST))} className="cursor-pointer hover:underline">{rateData.single_room_rate.FLAT_FEE_COST}</span>{renderSearchStatusIcon(String(rateData.single_room_rate.FLAT_FEE_COST))}</p>
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                )}</p>
                                                                                                                {rateData.single_room_rate.ADDITIONAL_FEE_RULES && rateData.single_room_rate.ADDITIONAL_FEE_RULES.length > 0 && (
                                                                                                                    <div className="mt-2">
                                                                                                                        <p className="text-xs font-medium text-gray-700 mb-1">Additional Fee Rules:</p>
                                                                                                                        {rateData.single_room_rate.ADDITIONAL_FEE_RULES.map((rule, ruleIndex) => (
                                                                                                                            <div key={ruleIndex} className="text-xs text-gray-600 ml-2">
                                                                                                                                <span onClick={() => handleItemClick(rule.name)} className="cursor-pointer hover:underline">{rule.name}</span>: {formatCurrency(rateData.single_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rule.fee_amount))} className="cursor-pointer hover:underline">{rule.fee_amount}</span> {formatRateUnit(rule.fee_unit || 'per_person_per_night')} (after {rule.apply_after} guests)
                                                                                                                            </div>
                                                                                                                        ))}
                                                                                                                    </div>
                                                                                                                )}
                                                                                                                <p className="font-semibold text-green-600"><strong onClick={() => handleItemClick('Total Cost')} className="cursor-pointer hover:underline">Total Cost:</strong> {formatCurrency(rateData.single_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(calculateRoomTotalCost(rateData.single_room_rate)))} className="cursor-pointer hover:underline">{calculateRoomTotalCost(rateData.single_room_rate)}</span>{renderSearchStatusIcon(String(calculateRoomTotalCost(rateData.single_room_rate)))}</p>
                                                                                                            </div>
                                                                                                        )}
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    </div>
                                                                                )}

                                                                                {/* Double Room Rate - Display all rate types */}
                                                                                {currentRangeRates.double && typeof currentRangeRates.double === 'object' && Object.keys(currentRangeRates.double).length > 0 && Object.entries(currentRangeRates.double).length > 0 && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Double Room Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Double Room Rate</h5>
                                                                                        <div className="space-y-3">
                                                                                            {Object.entries(currentRangeRates.double).map(([rateTypeName, rateData]) => {
                                                                                                if (!rateData || !rateData.double_room_rate) return null;
                                                                                                return (
                                                                                                    <div key={rateTypeName} className="border-l-4 border-blue-200 pl-3">
                                                                                                        <h6 className="font-medium text-blue-700 mb-1">{rateTypeName}</h6>
                                                                                                        {editingRates ? (
                                                                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                                                                                                                <div>
                                                                                                                    <label htmlFor={`doubleBaseRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Rate Amount ({formatCurrency(rateData.double_room_rate.CURRENCY || 'ZAR')}):</label>
                                                                                                                    <input type="number" id={`doubleBaseRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.double_room_rate.BASE_RATE || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'double', 'BASE_RATE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`doubleRateUnit-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Rate Unit:</label>
                                                                                                                    <select id={`doubleRateUnit-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.double_room_rate.RATE_UNIT || 'room_per_night'} onChange={e => handleRateChange(periodIndex, rangeIndex, 'double', 'RATE_UNIT', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                                                    <option value="room_per_night">Room per night</option>
                                                                                    <option value="person_per_night">Person per night</option>
                                                                                </select>
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`doubleCurrency-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Currency:</label>
                                                                                                                    <select id={`doubleCurrency-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.double_room_rate.CURRENCY || 'ZAR'} onChange={e => handleRateChange(periodIndex, rangeIndex, 'double', 'CURRENCY', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                                                    <option value="ZAR">ZAR</option>
                                                                                    <option value="USD">USD</option>
                                                                                </select>
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`doubleMinStay-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Min Stay Nights:</label>
                                                                                                                    <input type="number" id={`doubleMinStay-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.double_room_rate.MIN_STAY_NIGHTS || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'double', 'MIN_STAY_NIGHTS', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                            
                                                                                                                <div>
                                                                                                                    <label htmlFor={`doubleAdditional-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Additional People (count):</label>
                                                                                                                    <input type="number" id={`doubleAdditional-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.double_room_rate.ADDITIONAL_PEOPLE || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'double', 'ADDITIONAL_PEOPLE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`doubleAdditionalPersonCost-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Cost per Additional Person:</label>
                                                                                                                    <input type="number" id={`doubleAdditionalPersonCost-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.double_room_rate.ADDITIONAL_PERSON_COST || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'double', 'ADDITIONAL_PERSON_COST', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div className="md:col-span-2">
                                                                                                                    <p className="text-xs text-gray-600 mb-1"><strong>Base Capacity:</strong> {safeRenderValue(rateData.double_room_rate.BASE_CAPACITY)} people</p>                                                             
                                                                                                                    <p className="font-semibold text-green-600 text-sm"><strong>Total Cost:</strong> {formatCurrency(rateData.double_room_rate.CURRENCY || 'ZAR')}{calculateRoomTotalCost(rateData.double_room_rate)}</p>
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        ) : (
                                                                                                            <div className="space-y-1">
                                                                                                                <p><strong onClick={() => handleItemClick('Double')} className="cursor-pointer hover:underline">Base Rate:</strong> {formatCurrency(rateData.double_room_rate.CURRENCY || 'ZAR')} <span onClick={() => handleItemClick(String(rateData.double_room_rate.BASE_RATE || ''))} className="cursor-pointer hover:underline">{rateData.double_room_rate.BASE_RATE !== null && rateData.double_room_rate.BASE_RATE !== '' ? rateData.double_room_rate.BASE_RATE : 'N/A'}</span>{renderSearchStatusIcon(String(rateData.double_room_rate.BASE_RATE || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Rate Unit')} className="cursor-pointer hover:underline">Rate Unit:</strong> <span onClick={() => handleItemClick(String(rateData.double_room_rate.RATE_UNIT || ''))} className="cursor-pointer hover:underline">{formatRateUnit(rateData.double_room_rate.RATE_UNIT || 'room per night')}</span>{renderSearchStatusIcon(String(rateData.double_room_rate.RATE_UNIT || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Base Capacity')} className="cursor-pointer hover:underline">Base Capacity:</strong> <span onClick={() => handleItemClick(String(rateData.double_room_rate.BASE_CAPACITY || ''))} className="cursor-pointer hover:underline">{rateData.double_room_rate.BASE_CAPACITY !== undefined && rateData.double_room_rate.BASE_CAPACITY !== null && rateData.double_room_rate.BASE_CAPACITY !== '' ? rateData.double_room_rate.BASE_CAPACITY : 'N/A'}</span>{renderSearchStatusIcon(String(rateData.double_room_rate.BASE_CAPACITY || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Additional People')} className="cursor-pointer hover:underline">Additional People:</strong> <span onClick={() => handleItemClick(String(rateData.double_room_rate.ADDITIONAL_PEOPLE || ''))} className="cursor-pointer hover:underline">{rateData.double_room_rate.ADDITIONAL_PEOPLE !== undefined && rateData.double_room_rate.ADDITIONAL_PEOPLE !== null && rateData.double_room_rate.ADDITIONAL_PEOPLE !== '' ? rateData.double_room_rate.ADDITIONAL_PEOPLE : 'N/A'}</span>{renderSearchStatusIcon(String(rateData.double_room_rate.ADDITIONAL_PEOPLE || ''))}
                                                                                                                {(rateData.double_room_rate.ADDITIONAL_PERSON_COST > 0 || rateData.double_room_rate.FLAT_FEE_COST > 0) && (
                                                                                                                    <div className="ml-4 text-xs text-gray-600">
                                                                                                                        {rateData.double_room_rate.ADDITIONAL_PERSON_COST > 0 && (
                                                                                                                            <p><strong onClick={() => handleItemClick('Additional Person Cost')} className="cursor-pointer hover:underline">Per Additional Person:</strong> {formatCurrency(safeRenderValue(rateData.double_room_rate.CURRENCY, 'ZAR'))}<span onClick={() => handleItemClick(String(rateData.double_room_rate.ADDITIONAL_PERSON_COST))} className="cursor-pointer hover:underline">{rateData.double_room_rate.ADDITIONAL_PERSON_COST}</span>{renderSearchStatusIcon(String(rateData.double_room_rate.ADDITIONAL_PERSON_COST))}</p>
                                                                                                                        )}
                                                                                                                        {rateData.double_room_rate.FLAT_FEE_COST > 0 && (
                                                                                                                            <p><strong onClick={() => handleItemClick('Flat Fee')} className="cursor-pointer hover:underline">Flat Fee:</strong> {formatCurrency(safeRenderValue(rateData.double_room_rate.CURRENCY, 'ZAR'))}<span onClick={() => handleItemClick(String(rateData.double_room_rate.FLAT_FEE_COST))} className="cursor-pointer hover:underline">{rateData.double_room_rate.FLAT_FEE_COST}</span>{renderSearchStatusIcon(String(rateData.double_room_rate.FLAT_FEE_COST))}</p>
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                )}</p>
                                                                                                                {rateData.double_room_rate.ADDITIONAL_FEE_RULES && rateData.double_room_rate.ADDITIONAL_FEE_RULES.length > 0 && (
                                                                                                                    <div className="mt-2">
                                                                                                                        <p className="text-xs font-medium text-gray-700 mb-1">Additional Fee Rules:</p>
                                                                                                                        {rateData.double_room_rate.ADDITIONAL_FEE_RULES.map((rule, ruleIndex) => (
                                                                                                                            <div key={ruleIndex} className="text-xs text-gray-600 ml-2">
                                                                                                                                <span onClick={() => handleItemClick(rule.name)} className="cursor-pointer hover:underline">{rule.name}</span>: {formatCurrency(rateData.double_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rule.fee_amount))} className="cursor-pointer hover:underline">{rule.fee_amount}</span> {formatRateUnit(rule.fee_unit || 'per_person_per_night')} (after {rule.apply_after} guests)
                                                                                                                            </div>
                                                                                                                        ))}
                                                                                                                    </div>
                                                                                                                )}
                                                                                                                <p className="font-semibold text-green-600"><strong onClick={() => handleItemClick('Total Cost')} className="cursor-pointer hover:underline">Total Cost:</strong> {formatCurrency(rateData.double_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(calculateRoomTotalCost(rateData.double_room_rate)))} className="cursor-pointer hover:underline">{calculateRoomTotalCost(rateData.double_room_rate)}</span>{renderSearchStatusIcon(String(calculateRoomTotalCost(rateData.double_room_rate)))}</p>
                                                                                                            </div>
                                                                                                        )}
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    </div>
                                                                                )}
                                                                                {/* Triple Room Rate - Display all rate types */}
                                                                                {currentRangeRates.triple && typeof currentRangeRates.triple === 'object' && Object.keys(currentRangeRates.triple).length > 0 && Object.entries(currentRangeRates.triple).length > 0 && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Triple Room Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Triple Room Rate</h5>
                                                                                        <div className="space-y-3">
                                                                                            {Object.entries(currentRangeRates.triple).map(([rateTypeName, rateData]) => {
                                                                                                if (!rateData || !rateData.triple_room_rate) return null;
                                                                                                return (
                                                                                                    <div key={rateTypeName} className="border-l-4 border-orange-200 pl-3">
                                                                                                        <h6 className="font-medium text-orange-700 mb-1">{rateTypeName}</h6>
                                                                                                        {editingRates ? (
                                                                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                                                                                                                <div>
                                                                                                                    <label htmlFor={`tripleBaseRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Base Rate (R):</label>
                                                                                                                    <input type="number" id={`tripleBaseRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.triple_room_rate.BASE_RATE || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'triple', 'BASE_RATE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`tripleRateUnit-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Rate Unit:</label>
                                                                                                                    <select id={`tripleRateUnit-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.triple_room_rate.RATE_UNIT || 'room_per_night'} onChange={e => handleRateChange(periodIndex, rangeIndex, 'triple', 'RATE_UNIT', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                                                    <option value="room_per_night">Room per night</option>
                                                                                    <option value="person_per_night">Person per night</option>
                                                                                </select>
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`tripleCurrency-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Currency:</label>
                                                                                                                    <select id={`tripleCurrency-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.triple_room_rate.CURRENCY || 'ZAR'} onChange={e => handleRateChange(periodIndex, rangeIndex, 'triple', 'CURRENCY', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                                                    <option value="ZAR">ZAR</option>
                                                                                    <option value="USD">USD</option>
                                                                                </select>
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`tripleMinStay-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Min Stay Nights:</label>
                                                                                                                    <input type="number" id={`tripleMinStay-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.triple_room_rate.MIN_STAY_NIGHTS || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'triple', 'MIN_STAY_NIGHTS', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`tripleAdditional-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Additional People (count):</label>
                                                                                                                    <input type="number" id={`tripleAdditional-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.triple_room_rate.ADDITIONAL_PEOPLE || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'triple', 'ADDITIONAL_PEOPLE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`tripleAdditionalPersonCost-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Cost per Additional Person:</label>
                                                                                                                    <input type="number" id={`tripleAdditionalPersonCost-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.triple_room_rate.ADDITIONAL_PERSON_COST || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'triple', 'ADDITIONAL_PERSON_COST', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div className="md:col-span-2">
                                                                                                                    <p className="text-xs text-gray-600 mb-1"><strong>Base Capacity:</strong> {safeRenderValue(rateData.triple_room_rate.BASE_CAPACITY)} people</p>                                                             
                                                                                                                    <p className="font-semibold text-green-600 text-sm"><strong>Total Cost:</strong> {formatCurrency(rateData.triple_room_rate.CURRENCY || 'ZAR')}{calculateRoomTotalCost(rateData.triple_room_rate)}</p>
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        ) : (
                                                                                                            <div className="space-y-1">
                                                                                                                <p><strong onClick={() => handleItemClick('Base Rate')} className="cursor-pointer hover:underline">Base Rate:</strong> R<span onClick={() => handleItemClick(String(rateData.triple_room_rate.BASE_RATE || ''))} className="cursor-pointer hover:underline">{rateData.triple_room_rate.BASE_RATE !== null && rateData.triple_room_rate.BASE_RATE !== '' ? rateData.triple_room_rate.BASE_RATE : 'N/A'}</span>{renderSearchStatusIcon(String(rateData.triple_room_rate.BASE_RATE || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Rate Unit')} className="cursor-pointer hover:underline">Rate Unit:</strong> <span onClick={() => handleItemClick(String(rateData.triple_room_rate.RATE_UNIT || ''))} className="cursor-pointer hover:underline">{formatRateUnit(rateData.triple_room_rate.RATE_UNIT || 'room per night')}</span>{renderSearchStatusIcon(String(rateData.triple_room_rate.RATE_UNIT || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Base Capacity')} className="cursor-pointer hover:underline">Base Capacity:</strong> <span onClick={() => handleItemClick(String(rateData.triple_room_rate.BASE_CAPACITY || ''))} className="cursor-pointer hover:underline">{rateData.triple_room_rate.BASE_CAPACITY !== null && rateData.triple_room_rate.BASE_CAPACITY !== '' ? rateData.triple_room_rate.BASE_CAPACITY : 'N/A'}</span> people{renderSearchStatusIcon(String(rateData.triple_room_rate.BASE_CAPACITY || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Additional People')} className="cursor-pointer hover:underline">Additional People:</strong> <span onClick={() => handleItemClick(String(rateData.triple_room_rate.ADDITIONAL_PEOPLE || ''))} className="cursor-pointer hover:underline">{rateData.triple_room_rate.ADDITIONAL_PEOPLE !== null && rateData.triple_room_rate.ADDITIONAL_PEOPLE !== '' ? rateData.triple_room_rate.ADDITIONAL_PEOPLE : 'N/A'}</span> people{renderSearchStatusIcon(String(rateData.triple_room_rate.ADDITIONAL_PEOPLE || ''))}
                                                                                                                {(rateData.triple_room_rate.ADDITIONAL_PERSON_COST > 0 || rateData.triple_room_rate.FLAT_FEE_COST > 0) && (
                                                                                                                    <div className="ml-4 text-xs text-gray-600">
                                                                                                                        {rateData.triple_room_rate.ADDITIONAL_PERSON_COST > 0 && (
                                                                                                                            <p><strong onClick={() => handleItemClick('Additional Person Cost')} className="cursor-pointer hover:underline">Per Additional Person:</strong> {formatCurrency(rateData.triple_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rateData.triple_room_rate.ADDITIONAL_PERSON_COST))} className="cursor-pointer hover:underline">{rateData.triple_room_rate.ADDITIONAL_PERSON_COST}</span>{renderSearchStatusIcon(String(rateData.triple_room_rate.ADDITIONAL_PERSON_COST))}</p>
                                                                                                                        )}
                                                                                                                        {rateData.triple_room_rate.FLAT_FEE_COST > 0 && (
                                                                                                                            <p><strong onClick={() => handleItemClick('Flat Fee')} className="cursor-pointer hover:underline">Flat Fee:</strong> {formatCurrency(rateData.triple_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rateData.triple_room_rate.FLAT_FEE_COST))} className="cursor-pointer hover:underline">{rateData.triple_room_rate.FLAT_FEE_COST}</span>{renderSearchStatusIcon(String(rateData.triple_room_rate.FLAT_FEE_COST))}</p>
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                )}</p>
                                                                                                                <p className="font-semibold text-green-600"><strong onClick={() => handleItemClick('Total Cost')} className="cursor-pointer hover:underline">Total Cost:</strong> {formatCurrency(rateData.triple_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(calculateRoomTotalCost(rateData.triple_room_rate)))} className="cursor-pointer hover:underline">{calculateRoomTotalCost(rateData.triple_room_rate)}</span>{renderSearchStatusIcon(String(calculateRoomTotalCost(rateData.triple_room_rate)))}</p>
                                                                                                            </div>
                                                                                                        )}
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    </div>
                                                                                )}

                                                                                {/* Quad Room Rate - Display all rate types */}
                                                                                {currentRangeRates.quad && typeof currentRangeRates.quad === 'object' && Object.keys(currentRangeRates.quad).length > 0 && Object.entries(currentRangeRates.quad).length > 0 && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Quad Room Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Quad Room Rate</h5>
                                                                                        <div className="space-y-3">
                                                                                            {Object.entries(currentRangeRates.quad).map(([rateTypeName, rateData]) => {
                                                                                                if (!rateData || !rateData.quad_room_rate) return null;
                                                                                                return (
                                                                                                    <div key={rateTypeName} className="border-l-4 border-red-200 pl-3">
                                                                                                        <h6 className="font-medium text-red-700 mb-1">{rateTypeName}</h6>
                                                                                                        {editingRates ? (
                                                                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
                                                                                                                <div>
                                                                                                                    <label htmlFor={`quadBaseRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Base Rate (R):</label>
                                                                                                                    <input type="number" id={`quadBaseRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.quad_room_rate.BASE_RATE || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'quad', 'BASE_RATE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`quadRateUnit-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Rate Unit:</label>
                                                                                                                    <select id={`quadRateUnit-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.quad_room_rate.RATE_UNIT || 'room_per_night'} onChange={e => handleRateChange(periodIndex, rangeIndex, 'quad', 'RATE_UNIT', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                                                    <option value="room_per_night">Room per night</option>
                                                                                    <option value="person_per_night">Person per night</option>
                                                                                </select>
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`quadCurrency-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Currency:</label>
                                                                                                                    <select id={`quadCurrency-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.quad_room_rate.CURRENCY || 'ZAR'} onChange={e => handleRateChange(periodIndex, rangeIndex, 'quad', 'CURRENCY', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                                                                    <option value="ZAR">ZAR</option>
                                                                                    <option value="USD">USD</option>
                                                                                </select>
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`quadMinStay-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Min Stay Nights:</label>
                                                                                                                    <input type="number" id={`quadMinStay-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.quad_room_rate.MIN_STAY_NIGHTS || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'quad', 'MIN_STAY_NIGHTS', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`quadAdditional-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Additional People (count):</label>
                                                                                                                    <input type="number" id={`quadAdditional-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.quad_room_rate.ADDITIONAL_PEOPLE || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'quad', 'ADDITIONAL_PEOPLE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div>
                                                                                                                    <label htmlFor={`quadAdditionalPersonCost-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Cost per Additional Person:</label>
                                                                                                                    <input type="number" id={`quadAdditionalPersonCost-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.quad_room_rate.ADDITIONAL_PERSON_COST || ''} onChange={e => handleRateChange(periodIndex, rangeIndex, 'quad', 'ADDITIONAL_PERSON_COST', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                </div>
                                                                                                                <div className="md:col-span-2">
                                                                                                                    <p className="text-xs text-gray-600 mb-1"><strong>Base Capacity:</strong> {safeRenderValue(rateData.quad_room_rate.BASE_CAPACITY)} people</p>                                                             
                                                                                                                    <p className="font-semibold text-green-600 text-sm"><strong>Total Cost:</strong> {formatCurrency(rateData.quad_room_rate.CURRENCY || 'ZAR')}{calculateRoomTotalCost(rateData.quad_room_rate)}</p>
                                                                                                                </div>
                                                                                                            </div>
                                                                                                        ) : (
                                                                                                            <div className="space-y-1">
                                                                                                                <p><strong onClick={() => handleItemClick('Base Rate')} className="cursor-pointer hover:underline">Base Rate:</strong> R<span onClick={() => handleItemClick(String(rateData.quad_room_rate.BASE_RATE || ''))} className="cursor-pointer hover:underline">{rateData.quad_room_rate.BASE_RATE !== null && rateData.quad_room_rate.BASE_RATE !== '' ? rateData.quad_room_rate.BASE_RATE : 'N/A'}</span>{renderSearchStatusIcon(String(rateData.quad_room_rate.BASE_RATE || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Rate Unit')} className="cursor-pointer hover:underline">Rate Unit:</strong> <span onClick={() => handleItemClick(String(rateData.quad_room_rate.RATE_UNIT || ''))} className="cursor-pointer hover:underline">{formatRateUnit(rateData.quad_room_rate.RATE_UNIT || 'room per night')}</span>{renderSearchStatusIcon(String(rateData.quad_room_rate.RATE_UNIT || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Base Capacity')} className="cursor-pointer hover:underline">Base Capacity:</strong> <span onClick={() => handleItemClick(String(rateData.quad_room_rate.BASE_CAPACITY || ''))} className="cursor-pointer hover:underline">{rateData.quad_room_rate.BASE_CAPACITY || 'N/A'}</span> people{renderSearchStatusIcon(String(rateData.quad_room_rate.BASE_CAPACITY || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Additional People')} className="cursor-pointer hover:underline">Additional People:</strong> <span onClick={() => handleItemClick(String(rateData.quad_room_rate.ADDITIONAL_PEOPLE || ''))} className="cursor-pointer hover:underline">{rateData.quad_room_rate.ADDITIONAL_PEOPLE !== null && rateData.quad_room_rate.ADDITIONAL_PEOPLE !== '' ? rateData.quad_room_rate.ADDITIONAL_PEOPLE : 'N/A'}</span> people{renderSearchStatusIcon(String(rateData.quad_room_rate.ADDITIONAL_PEOPLE || ''))}
                                                                                                                {(rateData.quad_room_rate.ADDITIONAL_PERSON_COST > 0 || rateData.quad_room_rate.FLAT_FEE_COST > 0) && (
                                                                                                                    <div className="ml-4 text-xs text-gray-600">
                                                                                                                        {rateData.quad_room_rate.ADDITIONAL_PERSON_COST > 0 && (
                                                                                                                            <p><strong onClick={() => handleItemClick('Additional Person Cost')} className="cursor-pointer hover:underline">Per Additional Person:</strong> {formatCurrency(rateData.quad_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rateData.quad_room_rate.ADDITIONAL_PERSON_COST))} className="cursor-pointer hover:underline">{rateData.quad_room_rate.ADDITIONAL_PERSON_COST}</span>{renderSearchStatusIcon(String(rateData.quad_room_rate.ADDITIONAL_PERSON_COST))}</p>
                                                                                                                        )}
                                                                                                                        {rateData.quad_room_rate.FLAT_FEE_COST > 0 && (
                                                                                                                            <p><strong onClick={() => handleItemClick('Flat Fee')} className="cursor-pointer hover:underline">Flat Fee:</strong> {formatCurrency(rateData.quad_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rateData.quad_room_rate.FLAT_FEE_COST))} className="cursor-pointer hover:underline">{rateData.quad_room_rate.FLAT_FEE_COST}</span>{renderSearchStatusIcon(String(rateData.quad_room_rate.FLAT_FEE_COST))}</p>
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                )}</p>
                                                                                                                <p className="font-semibold text-green-600"><strong onClick={() => handleItemClick('Total Cost')} className="cursor-pointer hover:underline">Total Cost:</strong> {formatCurrency(rateData.quad_room_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(calculateRoomTotalCost(rateData.quad_room_rate)))} className="cursor-pointer hover:underline">{calculateRoomTotalCost(rateData.quad_room_rate)}</span>{renderSearchStatusIcon(String(calculateRoomTotalCost(rateData.quad_room_rate)))}</p>
                                                                                                            </div>
                                                                                                        )}
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    </div>
                                                                                )}

                                                                                {/* Child Rates - Display all rate types and categories */}
                                                                                {currentRangeRates.child && typeof currentRangeRates.child === 'object' && Object.keys(currentRangeRates.child).length > 0 && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Child Rates')} className="font-medium mb-2 cursor-pointer hover:underline">Child Rates</h5>
                                                                                        <div className="space-y-3">
                                                                                            {Object.entries(currentRangeRates.child).map(([rateTypeName, rateData]) => {
                                                                                                if (!rateData || !rateData.child_rate) return null;
                                                                                                return (
                                                                                                    <div key={rateTypeName} className="border-l-4 border-green-200 pl-3">
                                                                                                        <h6 className="font-medium text-green-700 mb-1">{rateTypeName}</h6>
                                                                                                        {rateData.child_rate.categories ? (
                                                                                                            // New format with multiple categories
                                                                                                            <div className="space-y-2">
                                                                                                                {Object.entries(rateData.child_rate.categories).map(([ageRange, categoryData]) => (
                                                                                                                    <div key={ageRange} className="bg-green-50 p-2 rounded">
                                                                                                                        <span className="font-medium text-green-800 mb-1">Age {ageRange}</span>
                                                                                                                        {editingRates ? (
                                                                                                                            <div>
                                                                                                                                <label htmlFor={`childRate-${periodIndex}-${rangeIndex}-${rateTypeName}-${ageRange}`} className="block text-xs font-medium text-gray-600">Rate ({categoryData?.currency || 'ZAR'}):</label>
                                                                                                                                <input type="number" id={`childRate-${periodIndex}-${rangeIndex}-${rateTypeName}-${ageRange}`} value={categoryData?.rate_amount || ''} onChange={(e) => handleChildCategoryRateChange(periodIndex, rangeIndex, rateTypeName, ageRange, 'rate_amount', e.target.value)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                                <p className="text-xs text-gray-600 mt-1"><strong>Rate Unit:</strong> {categoryData?.rate_unit || 'per_person_per_night'}</p>
                                                                                                                            </div>
                                                                                                                        ) : (
                                                                                                                            <div className="space-y-1">
                                                                                                                                <p><strong onClick={() => handleItemClick('Rate')} className="cursor-pointer hover:underline">Rate:</strong> {categoryData?.currency || 'ZAR'} <span onClick={() => handleItemClick(String(categoryData?.rate_amount || ''))} className="cursor-pointer hover:underline">{categoryData?.rate_amount !== null && categoryData?.rate_amount !== '' ? categoryData?.rate_amount : 'N/A'}</span>{renderSearchStatusIcon(String(categoryData?.rate_amount || ''))}</p>
                                                                                                                                <p><strong onClick={() => handleItemClick('Rate Unit')} className="cursor-pointer hover:underline">Rate Unit:</strong> <span onClick={() => handleItemClick(String(categoryData?.rate_unit || ''))} className="cursor-pointer hover:underline">{formatRateUnit(categoryData?.rate_unit || 'per_person_per_night')}</span>{renderSearchStatusIcon(String(categoryData?.rate_unit || ''))}</p>
                                                                                                                            </div>
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                ))}
                                                                                                            </div>
                                                                                                        ) : (
                                                                                                            // Fallback to old format for backward compatibility
                                                                                                            <div>
                                                                                                                {editingRates ? (
                                                                                                                    <div>
                                                                                                                        <label htmlFor={`childRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Rate ({formatCurrency(rateData.child_rate?.CURRENCY || 'ZAR')}):</label>
                                                                                                                        <input type="number" id={`childRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.child_rate?.RATE || ''} onChange={(e) => handleRateChange(periodIndex, rangeIndex, 'child', 'RATE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                        <p className="text-xs text-gray-600 mt-1"><strong>Rate Unit:</strong> {formatRateUnit(rateData.child_rate?.RATE_UNIT || 'per person per night')}</p>
                                                                                                                    </div>
                                                                                                                ) : (
                                                                                                                    <div className="space-y-1">
                                                                                                                        <p><strong onClick={() => handleItemClick('Rate')} className="cursor-pointer hover:underline">Rate:</strong> {formatCurrency(rateData.child_rate?.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rateData.child_rate?.RATE || ''))} className="cursor-pointer hover:underline">{rateData.child_rate?.RATE !== null && rateData.child_rate?.RATE !== '' ? rateData.child_rate?.RATE : 'N/A'}</span>{renderSearchStatusIcon(String(rateData.child_rate?.RATE || ''))}</p>
                                                                                                                        <p><strong onClick={() => handleItemClick('Rate Unit')} className="cursor-pointer hover:underline">Rate Unit:</strong> <span onClick={() => handleItemClick(String(rateData.child_rate?.RATE_UNIT || ''))} className="cursor-pointer hover:underline">{formatRateUnit(rateData.child_rate?.RATE_UNIT || 'per person per night')}</span>{renderSearchStatusIcon(String(rateData.child_rate?.RATE_UNIT || ''))}</p>
                                                                                                                        {rateData.child_rate?.ADDITIONAL_FEE_RULES && rateData.child_rate.ADDITIONAL_FEE_RULES.length > 0 && (
                                                                                                                            <div className="mt-2">
                                                                                                                                <p className="text-xs font-medium text-gray-700 mb-1">Additional Fee Rules:</p>
                                                                                                                                {rateData.child_rate.ADDITIONAL_FEE_RULES.map((rule, ruleIndex) => (
                                                                                                                                    <div key={ruleIndex} className="text-xs text-gray-600 ml-2">
                                                                                                                                        <span onClick={() => handleItemClick(rule.name)} className="cursor-pointer hover:underline">{rule.name}</span>: {formatCurrency(rateData.child_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rule.fee_amount))} className="cursor-pointer hover:underline">{rule.fee_amount}</span> {formatRateUnit(rule.fee_unit || 'per_person_per_night')} (after {rule.apply_after} guests)
                                                                                                                                    </div>
                                                                                                                                ))}
                                                                                                                            </div>
                                                                                                                        )}
                                                                                                                    </div>
                                                                                                                )}
                                                                                                            </div>
                                                                                                        )}
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    </div>
                                                                                )}

                                                                                {/* Infant Rate - Display all rate types */}
                                                                                {currentRangeRates.infant && typeof currentRangeRates.infant === 'object' && Object.keys(currentRangeRates.infant).length > 0 && (
                                                                                    <div className="bg-white p-3 rounded shadow-sm">
                                                                                        <h5 onClick={() => handleItemClick('Infant Rate')} className="font-medium mb-2 cursor-pointer hover:underline">Infant Rate</h5>
                                                                                        <div className="space-y-3">
                                                                                            {Object.entries(currentRangeRates.infant).map(([rateTypeName, rateData]) => {
                                                                                                if (!rateData || !rateData.infant_rate) return null;
                                                                                                return (
                                                                                                    <div key={rateTypeName} className="border-l-4 border-purple-200 pl-3">
                                                                                                        <h6 className="font-medium text-purple-700 mb-1">{rateTypeName}</h6>
                                                                                                        {editingRates ? (
                                                                                                            <div>
                                                                                                                <label htmlFor={`infantRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} className="block text-xs font-medium text-gray-600">Rate ({formatCurrency(rateData.infant_rate?.CURRENCY || 'ZAR')}):</label>
                                                                                                                <input type="number" id={`infantRate-${periodIndex}-${rangeIndex}-${rateTypeName}`} value={rateData.infant_rate?.RATE || ''} onChange={(e) => handleRateChange(periodIndex, rangeIndex, 'infant', 'RATE', e.target.value, rateTypeName)} className="mt-1 block w-full px-2 py-1 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="0" />
                                                                                                                <p className="text-xs text-gray-600 mt-1"><strong>Rate Unit:</strong> {formatRateUnit(rateData.infant_rate?.RATE_UNIT || 'per person per night')}</p>
                                                                                                            </div>
                                                                                                        ) : (
                                                                                                            <div className="space-y-1">
                                                                                                                <p><strong onClick={() => handleItemClick('Rate')} className="cursor-pointer hover:underline">Rate:</strong> {formatCurrency(rateData.infant_rate?.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rateData.infant_rate?.RATE || ''))} className="cursor-pointer hover:underline">{rateData.infant_rate?.RATE !== null && rateData.infant_rate?.RATE !== '' ? rateData.infant_rate?.RATE : 'N/A'}</span>{renderSearchStatusIcon(String(rateData.infant_rate?.RATE || ''))}</p>
                                                                                                                <p><strong onClick={() => handleItemClick('Rate Unit')} className="cursor-pointer hover:underline">Rate Unit:</strong> <span onClick={() => handleItemClick(String(rateData.infant_rate?.RATE_UNIT || ''))} className="cursor-pointer hover:underline">{formatRateUnit(rateData.infant_rate?.RATE_UNIT || 'per person per night')}</span>{renderSearchStatusIcon(String(rateData.infant_rate?.RATE_UNIT || ''))}</p>
                                                                                                                {rateData.infant_rate?.ADDITIONAL_FEE_RULES && rateData.infant_rate.ADDITIONAL_FEE_RULES.length > 0 && (
                                                                                                                    <div className="mt-2">
                                                                                                                        <p className="text-xs font-medium text-gray-700 mb-1">Additional Fee Rules:</p>
                                                                                                                        {rateData.infant_rate.ADDITIONAL_FEE_RULES.map((rule, ruleIndex) => (
                                                                                                                            <div key={ruleIndex} className="text-xs text-gray-600 ml-2">
                                                                                                                                <span onClick={() => handleItemClick(rule.name)} className="cursor-pointer hover:underline">{rule.name}</span>: {formatCurrency(rateData.infant_rate.CURRENCY || 'ZAR')}<span onClick={() => handleItemClick(String(rule.fee_amount))} className="cursor-pointer hover:underline">{rule.fee_amount}</span> {formatRateUnit(rule.fee_unit || 'per_person_per_night')} (after {rule.apply_after} guests)
                                                                                                                            </div>
                                                                                                                        ))}
                                                                                                                    </div>
                                                                                                                )}
                                                                                                            </div>
                                                                                                        )}
                                                                                                    </div>
                                                                                                );
                                                                                            })}
                                                                                        </div>
                                                                                    </div>
                                                                                )}
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    ))}
                                                </div>
                                            ) : (
                                                <p className="text-gray-500">No rates available for editing or display.</p>
                                            )}
                                            {!editingRates && (
                                                <div className="flex justify-end mt-4">
                                                    <button onClick={handleRatesEdit} className="px-4 py-2 text-blue-500 hover:text-blue-700">Edit Rates</button>
                                                </div>
                                            )}
                                            {editingRates && ( // Save/Cancel for Rates at bottom of rates list as well
                                                <div className="flex justify-end gap-2 mt-4">
                                                    <button onClick={handleRatesCancel} className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100">Cancel Rates</button>
                                                    <button onClick={handleRatesSave} className="px-4 py-2 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600">Save Rates</button>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                        {/* "Next/Finish" Button */}
                        {workflowState && roomTypeList.length > 0 && !loading && ( // Show button when not loading and workflow is active
                             <div className="mt-6">
                                 <button
                                     onClick={handleProceed}
                                     className="w-full px-4 py-3 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 disabled:bg-gray-400"
                                     disabled={loading} // Disable while loading analysis
                                 >
                                     {currentRoomTypeIndex < roomTypeList.length - 1
                                         ? `Next Room Type (${currentRoomTypeIndex + 2}/${roomTypeList.length})`
                                         : (workflowState.currentPropertyIndex < workflowState.totalProperties - 1
                                             ? "Finish Property & Next Property"
                                             : "Finish Section"
                                           )
                                     }
                                 </button>
                             </div>
                        )}
                         {workflowState && roomTypeList.length === 0 && !loading && ( // Handle case with no rooms for property
                            <div className="mt-6">
                                <button
                                    onClick={handleProceed}
                                    className="w-full px-4 py-3 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 disabled:bg-gray-400"
                                    disabled={loading}
                                >
                                    {workflowState.currentPropertyIndex < workflowState.totalProperties - 1
                                        ? "No Rooms Found - Proceed to Next Property"
                                        : "No Rooms Found - Finish Processing"
                                    }
                                </button>
                            </div>
                         )}
                    </div>
                    
                    {/* Right Column - PDF Viewer */}
                    <div className="lg:col-span-3 bg-white p-6 rounded-lg shadow-md sticky top-6 max-h-[calc(100vh-3rem)] overflow-y-auto"> {/* Make PDF viewer sticky */}
                        <h2 className="text-xl font-semibold mb-4">PDF Viewer</h2>
                        {pdfUrl ? <EnhancedPdfViewer url={pdfUrl} searchText={searchText} searchStatus={searchStatus} onSearchResult={handleSearchResult} /> : <p>No PDF loaded.</p>}
                    </div>
                </div>
            </div>
            {/* Capacity Decrease Modal */}
            {showCapacityDecreaseModal && (
                <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-40 z-50">
                    <div className="bg-white p-6 rounded shadow-lg max-w-md w-full">
                        <h2 className="text-lg font-bold mb-2 text-red-600">Capacity Decreased</h2>
                        <p className="mb-4">Changing capacity will refetch rates and any unsaved rate changes will be lost. Do you want to proceed?</p>
                        <div className="flex justify-end gap-2">
                            <button onClick={handleCapacityDecreaseReject} className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">Cancel</button>
                            <button onClick={handleCapacityDecreaseAccept} className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">Accept</button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );

    // List of final acceptable rows for backend:
    //CSV_HEADER = [
    //     "Supplier","Property", "Room type", "Meal basis", "DATE_FROM", "DATE_TO", "Min stay", "Includes", "Excludes",  "Period", 
    //     "Child 1 From age", "Child 1 To age", "Child 2 From age", "Child 2 To age", "Child 3 From age", "Child 3 To age", "Child 4 From age", "Child 4 To age", "Child 5 From age", "Child 5 To age",
    //     "Max adults", "Max Children","Max A+C",
    //     "Cancellation Policy from days 1", "Cancellation fee % 1", "Cancellation Policy from days 2", "Cancellation fee % 2", "Cancellation Policy from days 3", "Cancellation fee % 3", "Cancellation Policy from days 4", "Cancellation fee % 4",
    //     "Pay stay days 1", "Pay stay free nights 1", "Pay stay days 2", "Pay stay free nights 2", "Pay stay days 3", "Pay stay free nights 3", "Pay stay days 4", "Pay stay free nights 4",
    //     "Single room rate", "Double room rate", "Triple room rate", "Quad room rate", "Child rate 1", "Child rate 2", "Infant rate"
    // ]
    function generateRows() {
        console.log("Room Type Analysis for CSV:", roomTypeAnalysis);
        console.log("Editable Room Rates for CSV:", editableRoomRates);

        const sanitizePeriodName = (name) => {
            if (!name) return "UnknownPeriod";
            return name.replace(/[^a-zA-Z0-9_]/g, '_');
        };

        // Get data arrays
        const cancellation_policies = editableCancellationPolicies || [];
        const room_Capacity_Data = editableRoomCapacity || { MAX_ADULTS: '', MAX_CHILDREN: '', TOTAL_CAPACITY: '' };
        const pay_stay_offers = editablePayStayOffers || [];
        const child_age_ranges = JSON.parse(localStorage.getItem('childAgeRanges') || "[]");

        // Use editableMealType for CSV generation
        const meal_type = editableMealType !== null ? editableMealType : "";

        const includes = (JSON.parse(localStorage.getItem('includes') || "[]")).map(item => item.trim()).join(', ');
        const excludes = (JSON.parse(localStorage.getItem('excludes') || "[]")).map(item => item.trim()).join(', ');

        // Start with basic common fields
        const commonFields = {
            "Supplier": "", // Add supplier field to match backend
            "Property": selectedProperty || "",
            "Room type": selectedRoomType || "",
            "Meal basis": meal_type,
            "DATE_FROM": "", // Will be filled per row
            "DATE_TO": "", // Will be filled per row
            "Min stay": "", // Will be filled per row from rate data
            "Includes": includes,
            "Excludes": excludes,
            "Period": "", // Will be filled per row
        };

        // Dynamically add child age range columns
        for (let i = 0; i < Math.max(child_age_ranges.length, 5); i++) { // Support up to 5 child ranges like backend
            const childRange = child_age_ranges[i];
            commonFields[`Child ${i + 1} From age`] = (childRange && childRange[0] !== undefined) ? childRange[0] : "";
            commonFields[`Child ${i + 1} To age`] = (childRange && childRange[1] !== undefined) ? childRange[1] : "";
        }

        // Add capacity fields
        commonFields["Max adults"] = room_Capacity_Data && room_Capacity_Data.MAX_ADULTS !== undefined ? room_Capacity_Data.MAX_ADULTS : "";
        commonFields["Max Children"] = room_Capacity_Data && room_Capacity_Data.MAX_CHILDREN !== undefined ? room_Capacity_Data.MAX_CHILDREN : "";
        commonFields["Max A+C"] = room_Capacity_Data && room_Capacity_Data.TOTAL_CAPACITY !== undefined ? room_Capacity_Data.TOTAL_CAPACITY : "";

        // Dynamically add cancellation policy columns
        for (let i = 0; i < Math.max(cancellation_policies.length, 4); i++) { // Support up to 4 policies like backend
            const policy = cancellation_policies[i];
            commonFields[`Cancellation Policy from days ${i + 1}`] = (policy && policy.START_DAY !== undefined) ? policy.START_DAY : "";
            commonFields[`Cancellation fee % ${i + 1}`] = (policy && policy.CANCELLATION_FEE !== undefined) ? policy.CANCELLATION_FEE : "";
        }

        // Add pay stay columns (these will be filled per row based on period)
        commonFields["Pay stay days"] = "";
        commonFields["Pay stay free nights"] = "";

        // Add room rate columns (these will be filled per row)
        commonFields["Single room rate"] = "";
        commonFields["Double room rate"] = "";
        commonFields["Triple room rate"] = "";
        commonFields["Quad room rate"] = "";
        commonFields["Child rate 1"] = "";
        commonFields["Child rate 2"] = "";
        commonFields["Infant rate"] = "";



        const rows = [];

        // Generate a row for each period, date range, and rate type
        editableRoomRates.forEach((periodData) => {
            periodData.dateRanges.forEach((dateRange, rangeIndex) => {
                const currentRangeRates = periodData.rates[rangeIndex];
                if (!currentRangeRates) return;

                // Get all available rate types from the current range rates
                const availableRateTypes = new Set();

                // Check each room rate category for available rate types
                ['single', 'double', 'triple', 'quad', 'child', 'infant'].forEach(roomRateType => {
                    if (currentRangeRates[roomRateType] && typeof currentRangeRates[roomRateType] === 'object') {
                        Object.keys(currentRangeRates[roomRateType]).forEach(rateType => {
                            if (currentRangeRates[roomRateType][rateType] !== null) {
                                availableRateTypes.add(rateType);
                            }
                        });
                    }
                });

                // If no rate types found, create a single row with empty rates (backward compatibility)
                if (availableRateTypes.size === 0) {
                    const row = { ...commonFields };
                    row["Period"] = sanitizePeriodName(periodData.period) || "";
                    row["DATE_FROM"] = dateRange.start ? formatDateToDDMMYYYY(dateRange.start) : "";
                    row["DATE_TO"] = dateRange.end ? formatDateToDDMMYYYY(dateRange.end) : "";

                    // Find pay stay offers that apply to this period
                    const applicablePayStays = pay_stay_offers.filter(offer => {
                        if (!offer.PERIODS || offer.PERIODS.length === 0) {
                            return false; // Only include offers with specific periods
                        }
                        // Check if any of the offer's periods match the current period
                        return offer.PERIODS.some(period => period[0] === periodData.period);
                    });

                    // Add pay stay information for this period
                    if (applicablePayStays.length > 0) {
                        // If multiple offers apply, combine them (or take the first one)
                        const firstApplicableOffer = applicablePayStays[0];
                        row["Pay stay days"] = firstApplicableOffer.PAY_STAY_DAYS || "";
                        row["Pay stay free nights"] = firstApplicableOffer.PAY_STAY_FREE_NIGHTS || "";
                    } else {
                        row["Pay stay days"] = "";
                        row["Pay stay free nights"] = "";
                    }

                    rows.push(row);
                    return;
                }

                // Create a separate row for each rate type
                availableRateTypes.forEach(rateTypeName => {
                    const row = { ...commonFields };

                    // Add period and date range information with rate type appended to period name
                    if (rateTypeName.toUpperCase() != "NORMAL") {
                        row["Period"] = `${sanitizePeriodName(periodData.period)} - ${rateTypeName}`;
                    }
                    console.log("date range:", dateRange)
                    //format as DD/MM/YYYY
                    row["DATE_FROM"] = dateRange.start ? formatDateToDDMMYYYY(dateRange.start) : "";
                    row["DATE_TO"] = dateRange.end ? formatDateToDDMMYYYY(dateRange.end) : "";

                    // Get min stay nights from the single room rate for this specific rate type and period
                    let minStayNights = "";
                    const singleRateData = currentRangeRates.single && currentRangeRates.single[rateTypeName];
                    if (singleRateData && singleRateData.single_room_rate && singleRateData.single_room_rate.MIN_STAY_NIGHTS !== undefined) {
                        minStayNights = singleRateData.single_room_rate.MIN_STAY_NIGHTS;
                    }
                    row["Min stay"] = minStayNights;

                    // Find pay stay offers that apply to this period
                    const applicablePayStays = pay_stay_offers.filter(offer => {
                        if (!offer.PERIODS || offer.PERIODS.length === 0) {
                            return false; // Only include offers with specific periods
                        }
                        // Check if any of the offer's periods match the current period
                        return offer.PERIODS.some(period => period[0] === periodData.period);
                    });

                    // Add pay stay information for this period
                    if (applicablePayStays.length > 0) {
                        // If multiple offers apply, combine them (or take the first one)
                        const firstApplicableOffer = applicablePayStays[0];
                        row["Pay stay days"] = firstApplicableOffer.PAY_STAY_DAYS || "";
                        row["Pay stay free nights"] = firstApplicableOffer.PAY_STAY_FREE_NIGHTS || "";
                    } else {
                        row["Pay stay days"] = "";
                        row["Pay stay free nights"] = "";
                    }

                    // Add room rates for this specific rate type
                    const roomRateTypes = ['single', 'double', 'triple', 'quad'];
                    roomRateTypes.forEach(roomRateType => {
                        const rateTypeData = currentRangeRates[roomRateType] && currentRangeRates[roomRateType][rateTypeName];
                        if (rateTypeData && rateTypeData[`${roomRateType}_room_rate`]) {
                            const rateInfo = rateTypeData[`${roomRateType}_room_rate`];
                            // Calculate total rate using the helper function
                            const totalRate = calculateRoomTotalCost(rateInfo);
                            // Use the correct column name format to match backend
                            row[`${roomRateType.charAt(0).toUpperCase() + roomRateType.slice(1)} room rate`] = totalRate !== undefined && totalRate !== null ? totalRate : "";
                        }
                    });

                    // Add child rates for this specific rate type
                    const childRateData = currentRangeRates.child && currentRangeRates.child[rateTypeName];
                    if (childRateData && childRateData.child_rate) {
                        if (childRateData.child_rate.categories) {
                            // New format with multiple categories
                            const childAgeRanges = JSON.parse(localStorage.getItem('childAgeRanges') || "[]");
                            childAgeRanges.forEach((ageRange, index) => {
                                const ageRangeKey = `${ageRange[0]}-${ageRange[1]}`;
                                const categoryData = childRateData.child_rate.categories[ageRangeKey];
                                if (categoryData) {
                                    const rate = categoryData.rate_amount || 0;
                                    row[`Child rate ${index + 1}`] = rate !== undefined && rate !== null ? rate : "";
                                } else {
                                    row[`Child rate ${index + 1}`] = "";
                                }
                            });
                        } else {
                            // Fallback to old format for backward compatibility
                            let rate = childRateData.child_rate.RATE || 0;
                            // Add additional fees from fee rules if any
                            if (childRateData.child_rate.ADDITIONAL_FEE_RULES && Array.isArray(childRateData.child_rate.ADDITIONAL_FEE_RULES)) {
                                childRateData.child_rate.ADDITIONAL_FEE_RULES.forEach(rule => {
                                    if (rule.fee_amount) {
                                        rate += rule.fee_amount;
                                    }
                                });
                            }
                            row[`Child rate 1`] = rate !== undefined && rate !== null ? rate : "";
                        }
                    }

                    // Add infant rates for this specific rate type
                    const infantRateData = currentRangeRates.infant && currentRangeRates.infant[rateTypeName];
                    if (infantRateData && infantRateData.infant_rate) {
                        let rate = infantRateData.infant_rate.RATE || 0;
                        // Add additional fees from fee rules if any
                        if (infantRateData.infant_rate.ADDITIONAL_FEE_RULES && Array.isArray(infantRateData.infant_rate.ADDITIONAL_FEE_RULES)) {
                            infantRateData.infant_rate.ADDITIONAL_FEE_RULES.forEach(rule => {
                                if (rule.fee_amount) {
                                    rate += rule.fee_amount;
                                }
                            });
                        }
                        row[`Infant rate`] = rate !== undefined && rate !== null ? rate : "";
                    }

                    rows.push(row);
                });
            });
        });

        console.log("Generated CSV rows:", rows);
        return rows;
    }
}

export default RoomTypeAnalysis; 